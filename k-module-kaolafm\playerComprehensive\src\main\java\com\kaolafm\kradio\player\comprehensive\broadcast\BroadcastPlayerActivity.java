package com.kaolafm.kradio.player.comprehensive.broadcast;

import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.constraintlayout.widget.Group;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.common.ViewConstants;
import com.kaolafm.kradio.common.base.BaseSkinAppCompatActivity;
import com.kaolafm.kradio.common.event.PlayerPauseOrStartEBData;
import com.kaolafm.kradio.lib.report.ReportParamUtil;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.common.widget.TimerSeekBar;
import com.kaolafm.kradio.common.widget.layoutmanager.CenterSnapHelper;
import com.kaolafm.kradio.common.widget.layoutmanager.ScaleLayoutManager;
import com.kaolafm.kradio.common.widget.layoutmanager.ViewPagerLayoutManager;
import com.kaolafm.kradio.player.comprehensive.PlayerFragmentHelper;
import com.kaolafm.kradio.player.comprehensive.play.view.PlayerControlViewNext;
import com.kaolafm.kradio.player.comprehensive.play.view.PlayerControlViewPrevious;
import com.kaolafm.kradio.player.comprehensive.play.widget.BroadcastPlayListContent;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioMultiWindowInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioPlayPageModifyInter;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseHolder;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewUtils;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.manager.IBroadcastListChangeListener;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IGeneralListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListControl;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.BroadcastPlayListControl;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.event.PlayerUiControlReportEvent;

import org.greenrobot.eventbus.EventBus;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

import static com.kaolafm.kradio.lib.player.contant.BroadcastStatus.BROADCAST_STATUS_PLAYBACK;

/**
 * Created by kaolafm on 2018/10/16.
 */

public class BroadcastPlayerActivity extends BaseSkinAppCompatActivity<BroadcastPlayerPresenter>
        implements BroadcastPlayerView, RecyclerViewExposeUtil.OnItemExposeListener {
    private static final String TAG = "BroadcastPlayerFragment";

    RecyclerView mDsvBroadcastChannels;
    TimerSeekBar mBroadcastSeekBar;
    public BroadcastPlayListContent mPlayListView;
    Group mBroadcastShowHideGroup;
    TextView mBroadcastPlayerTitle;
    ConstraintLayout mRootLayout;
    ImageView mPlayerTitleLine;
    ImageView playList;
    public ConstraintLayout mPlayerControllerBar;
    TextView mSubscribeView;
    PlayerControlViewPrevious broadcast_play_pre_audio;
    PlayerControlViewNext broadcast_play_next_audio;

    private PlayerFragmentHelper mPlayerFragmentHelper;
    private boolean bIsSeeking;
    private int mSeekProgress;

    /**
     * 是否正在seek.
     */
    private boolean isSeeking;

    private ScaleLayoutManager mLayoutManager;

    private int currentScrollState;
    private PlayItem mCurrentPlayItem;

    private BroadcastIPlayerListChangedListener mBroadcastIPlayerListChangedListener;
    private BroadcastListChangeListener mBroadcastListChangeListener;

    private boolean mItemClick;
    private KRadioMultiWindowInter mKRadioMultiWindowInter;

    private void initSubscribe() {
        mPresenter.checkSubscribeMedia();
    }

    private void initListener() {
//        mBackView.setOnClickListener(v -> {
//            if (AntiShake.check(v.getId())) {
//                return;
//            }
//            if (!onBackPressedSupports()) {
//                finish();
//            }
//        });

        mSubscribeView.setOnClickListener(v -> {
            if (AntiShake.check(v.getId())) {
                return;
            }
            mPlayerFragmentHelper.subscribeOrUnSubscribeBroadcast(v, mPresenter, BroadcastPlayerActivity.this);
        });

        mBroadcastIPlayerListChangedListener = new BroadcastIPlayerListChangedListener(this);
        mBroadcastListChangeListener = new BroadcastListChangeListener(this);

        PlayerManager.getInstance().addPlayControlStateCallback(mPlayStateListener);
        PlayerManager.getInstance().addGeneralListener(mGeneralListener);
        PlayerManagerHelper.getInstance().addBroadcastListChangeListener(mBroadcastListChangeListener);
        PlayerManager.getInstance().addPlayListControlStateCallback(mBroadcastIPlayerListChangedListener);
    }

    /**
     * 更新错误的播放信息, 主要用于, 播单获取失败, 又要强行设置要播放的信息.
     */
    private void updateErrorPlayerInfo() {
        Log.i(TAG, "更新错误播单信息");
        int position = PlayerManagerHelper.getInstance().getCurrentFrequencyPosition();
        BroadcastRadioSimpleData broadcastRadioSimpleData = PlayerManagerHelper.getInstance().getBroadcastRadioSimpleDataByIndex(position);
        if (broadcastRadioSimpleData == null) {
            return;
        }
        if (mBroadcastPlayerTitle != null) {
            mBroadcastPlayerTitle.setText(broadcastRadioSimpleData.getName());
        }
        mBroadcastSeekBar.setTotalTime(-1);
        mBroadcastSeekBar.setElapsedTime(-1);


    }

    private boolean isPlayError() {
        PlayItem playItemTemp = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItemTemp.getType() != PlayerConstants.RESOURCES_TYPE_INVALID) {
            return false;
        }
        if (PlayerManagerHelper.getInstance().getCurrentPlayType() != PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            return false;
        }
        return true;
    }

    private void updatePlayerInfo() {
        if (isPlayError()) {
            updateErrorPlayerInfo();
            return;
        }

        PlayItem playItemTemp = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItemTemp == null || playItemTemp.getType() != PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            return;
        }
        BroadcastPlayItem playItem = (BroadcastPlayItem) playItemTemp;
        if (mBroadcastPlayerTitle != null) {
            String title;
            String freqChannel = playItem.getFrequencyChannel();
            if (TextUtils.isEmpty(freqChannel)) {
                title = playItem.getAlbumTitle();
                mBroadcastPlayerTitle.setText(title);
            } else {
                String name = playItem.getAlbumTitle();
                if (name.length() >= 8) {
                    name = name.substring(0, 7) + "...";
                }
                title = StringUtil.format(ResUtil.getString(R.string.one_zh_cn_char_joint_str),
                        name, ResUtil.getString(R.string.full_str), playItem.getFrequencyChannel());
                Spannable sp = new SpannableString(title);
                sp.setSpan(new AbsoluteSizeSpan(ResUtil.getDimen(R.dimen.m24), false),
                        title.length() - freqChannel.length(), title.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                sp.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.text_color_2)),
                        title.length() - freqChannel.length(), title.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                mBroadcastPlayerTitle.setText(sp);
            }
        }

        long start = playItem.getTimeInfoData().getStartTime();
        long end = playItem.getTimeInfoData().getFinishTime();
        int duration = (int) (end - start);
        if (PlayerManagerHelper.getInstance().isBroadcastPlayer() && !ListUtil.isEmpty(PlayerManager.getInstance().getPlayList())
                && PlayerManager.getInstance().getPlayList().get(0).getAudioId() == 0) {
            mBroadcastSeekBar.setTotalTime(-1);
            mBroadcastSeekBar.setElapsedTime(-1);
        } else {
            mBroadcastSeekBar.setElapsedTime(0);
            mBroadcastSeekBar.setTotalTime(duration);
            if (!playItem.isLivingUrl()) {
                mBroadcastSeekBar.setElapsedTime(playItem.getPosition());
            }
        }
        mBroadcastSeekBar.setMaxProgress(duration);
        if (!playItem.isLivingUrl()) {
            mBroadcastSeekBar.setProgress(playItem.getPosition());
        } else {
            mBroadcastSeekBar.setProgress(-1);
        }
        mBroadcastSeekBar.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (mBroadcastSeekBar == null) {
                    return;
                }
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                    mBroadcastSeekBar.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                } else {
                    legacyRemoveOnGlobalLayoutListener();
                }
                if (playItem.isLivingUrl()) {
                    mBroadcastSeekBar.post(() -> {
                        if (mBroadcastSeekBar != null) {
                            mBroadcastSeekBar.setCanTouch(false);
                        }
                    });
                } else {
                    mBroadcastSeekBar.setCanTouch(true);
                }
            }

            @SuppressWarnings("deprecation")
            private void legacyRemoveOnGlobalLayoutListener() {
                mBroadcastSeekBar.getViewTreeObserver().removeGlobalOnLayoutListener(this);
            }
        });
    }


    /**
     * 点击item事件上报
     *
     * @param item
     */
    private void reportContentClickEvent(BroadcastRadioSimpleData item) {
        ReportUtil.addContentClickEvent("",
                ReportParamUtil.getRadioType(ResType.BROADCAST_TYPE),
                "0", String.valueOf(item.getBroadcastId()),
                ReportParamUtil.getEventTag(false, false),
                Constants.PAGE_ID_PLAYER_MUSIC_LIST, "", "");
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_broadcast_player_horizontal;
    }

    @Override
    public int getLayoutId_Tow() {
        return 0;
    }

    @Override
    public void initView(Bundle savedInstanceState) {

        mDsvBroadcastChannels=findViewById(R.id.broadcast_player_horizontal_sv);
        mBroadcastSeekBar=findViewById(R.id.broadcast_player_horizontal_seek_bar);
        mPlayListView=findViewById(R.id.broadcast_player_horizontal_play_list);
        mBroadcastShowHideGroup=findViewById(R.id.broadcast_player_show_hide_group);
        mBroadcastPlayerTitle=findViewById(R.id.broadcast_player_horizontal_title_text);
        mRootLayout=findViewById(R.id.broadcast_player_root_layout);
        mPlayerTitleLine=findViewById(R.id.player_title_line);
        playList=findViewById(R.id.broadcast_playlist);
        mPlayerControllerBar=findViewById(R.id.broadcast_player_horizontal_controller_bar);
        mSubscribeView=findViewById(R.id.player_broadcast_subscribe_text);
        broadcast_play_pre_audio=findViewById(R.id.broadcast_play_pre_audio);
        broadcast_play_next_audio=findViewById(R.id.broadcast_play_next_audio);

        // 创建view
        mKRadioMultiWindowInter = ClazzImplUtil.getInter("KradioMultiWindowImpl");
        mPlayerFragmentHelper = new PlayerFragmentHelper();
        mLayoutManager = new ScaleLayoutManager.Builder(this, -ResUtil.getDimen(R.dimen.broadcast_item_space)).setMinScale(0.64f).setMoveSpeed(0.5f).build();
        mDsvBroadcastChannels.setLayoutManager(mLayoutManager);
        BroadcastChannelAdapter broadcastChannelAdapter = new BroadcastChannelAdapter();
        List<BroadcastRadioSimpleData> data = new ArrayList<>();
        data.addAll(PlayerManagerHelper.getInstance().getBroadcastRadioSimpleDataArrayList());
        broadcastChannelAdapter.setDataList(data);
        broadcastChannelAdapter.setOnItemClickListener((view1, viewType, broadcastRadioSimpleData, position) -> {
            if (mDsvBroadcastChannels != null && position != mLayoutManager.getCurrentPosition()) {
                mItemClick = true;
                mDsvBroadcastChannels.smoothScrollToPosition(position);
                reportContentClickEvent(broadcastRadioSimpleData);
            }
        });
        mDsvBroadcastChannels.setAdapter(broadcastChannelAdapter);
        RecyclerViewExposeUtil exposeUtil = new RecyclerViewExposeUtil();
        exposeUtil.setRecyclerItemExposeListener(mDsvBroadcastChannels, this);
        mDsvBroadcastChannels.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                mDsvBroadcastChannels.postDelayed(() -> {
                    if (mDsvBroadcastChannels!=null)
                    mDsvBroadcastChannels.scrollToPosition(PlayerManagerHelper.getInstance().getCurrentFrequencyPosition());
                }, 100);
                mDsvBroadcastChannels.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });
        KRadioPlayPageModifyInter inter = ClazzImplUtil.getInter("KRadioPlayPageModifyImpl");
        if (inter != null && inter.isModify()) {
            ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) mSubscribeView.getLayoutParams();
            lp.topMargin = ResUtil.getDimen(R.dimen.y30);
            mSubscribeView.setLayoutParams(lp);
        }
        initPlayListBtnView();
        initViewInner();
        initSubscribe();

        //
        mBroadcastSeekBar.setOnSeekBarChangeListener(mSeekBarListener);
        updatePlayListBtnEnabled(isHasBroadcastPlayList());
        updatePlayerInfo();
        initListener();

        //
        new CenterSnapHelper().attachToRecyclerView(mDsvBroadcastChannels);
        mLayoutManager.setOnPageChangeListener(new ViewPagerLayoutManager.OnPageChangeListener() {

            boolean mStart;

            BroadcastChannelAdapter.BroadcastChannelViewHolder mPreViewHolder;

            @Override
            public void onPageSelected(int position) {
                /*倒数第二个与最后一个切换时会出现不走onPageScrollStateChanged情况，此时需要
                在滑倒当前viewholder时将前一个状态重置 fix #34737
                */
                if (mPreViewHolder != null) {
                    mPreViewHolder.changeItemCheckedStatus(false);
                }
                BroadcastChannelAdapter.BroadcastChannelViewHolder viewHolder = getViewHolder();
                if (viewHolder != null) {
                    mPreViewHolder = viewHolder;
                    viewHolder.changeItemCheckedStatus(true);
                }
                if (!NetworkUtil.isNetworkAvailable(BroadcastPlayerActivity.this, true)) {
                    mItemClick = false;
                    return;
                }
                if (position != PlayerManagerHelper.getInstance().getCurrentFrequencyPosition()) {
                    if (mDsvBroadcastChannels != null) {
                        BroadcastChannelAdapter adapter = (BroadcastChannelAdapter) mDsvBroadcastChannels.getAdapter();
                        if (adapter != null) {
                            List<BroadcastRadioSimpleData> datas = adapter.getDataList();
                            if (datas.size() > position) {
                                BroadcastRadioSimpleData broadcastRadioSimpleData = null;
                                try {
                                    broadcastRadioSimpleData = datas.get(position);
                                    if (broadcastRadioSimpleData != null) {
                                        PlayerManagerHelper.getInstance().start(String.valueOf(broadcastRadioSimpleData.getBroadcastId()), PlayerConstants.RESOURCES_TYPE_BROADCAST);
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    Log.i(TAG, "onCurrentItemChanged: error=" + e);
                                }
                            }
                        }
                    }
                }

                mItemClick = false;
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                if (currentScrollState == RecyclerView.SCROLL_STATE_IDLE && state != currentScrollState) {
                    mStart = true;
                    if (mPreViewHolder == null) {
                        mPreViewHolder = getViewHolder();
                    }
                }
                currentScrollState = state;
            }

            @Override
            public void onScrolled(int dx, int dy) {
                if (mStart) {
                    if (mPreViewHolder != null) {
                        mPreViewHolder.changeItemCheckedStatus(false);
                    }
                    mStart = false;
                }
                /*无法滑动时，说明此时currentScrollState可能不为RecyclerView.SCROLL_STATE_IDLE，
                需要手动将其置为RecyclerView.SCROLL_STATE_IDLE，下次滑动时才会正确隐藏播放状态
                fix #34845
                 */
                if (mDsvBroadcastChannels != null
                        && ((!mDsvBroadcastChannels.canScrollHorizontally(1) && dx > 0)
                        || (!mDsvBroadcastChannels.canScrollHorizontally(-1) && dx < 0))) {
                    currentScrollState = RecyclerView.SCROLL_STATE_IDLE;
                }
            }
        });
        changeViewLayoutForStatusBar(mRootLayout);
    }

    @Override
    public void initData() {

    }

    @Override
    protected BroadcastPlayerPresenter createPresenter() {
        return new BroadcastPlayerPresenter(this);
    }

//    @Override
//    public boolean useEventBus() {
//        return true;
//    }

    @Override
    public void onDestroy() {
        Log.i("BroadcastPlayerFragment", "onDestroy");
        if (mPlayListView != null) {
            mPlayListView.onDestroy();
        }
        super.onDestroy();

        PlayerManager.getInstance().removePlayControlStateCallback(mPlayStateListener);
        PlayerManager.getInstance().removeGeneralListener(mGeneralListener);
        PlayerManagerHelper.getInstance().removeBroadcastListChangeListener(mBroadcastListChangeListener);
        PlayerManager.getInstance().removePlayListControlStateCallback(mBroadcastIPlayerListChangedListener);

        if (mPlayerFragmentHelper != null) {
            mPlayerFragmentHelper.onDestroyPlayerFragmentHelper();
        }
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_PLAYER_MUSIC_LIST;
    }

    private SeekBar.OnSeekBarChangeListener mSeekBarListener = new SeekBar.OnSeekBarChangeListener() {

        @Override
        public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
            if (fromUser) {
                Log.i(TAG, "mSeekBarListener onProgressChanged: " + progress);
                mSeekProgress = progress;
            }
        }

        @Override
        public void onStartTrackingTouch(SeekBar seekBar) {
            bIsSeeking = true;
        }

        @Override
        public void onStopTrackingTouch(SeekBar seekBar) {
            bIsSeeking = false;
            if (mSeekProgress == seekBar.getMax()) {
                mSeekProgress = seekBar.getMax() - 5000;
                playNextBroadcastItem(mSeekProgress);
            } else {
                PlayerManagerHelper.getInstance().seek(mSeekProgress);
            }
        }

    };

    private void playNextBroadcastItem(int seekProgress) {
        IPlayListControl iPlayListControl = PlayerManager.getInstance().getPlayListControl();
        if (iPlayListControl instanceof BroadcastPlayListControl) {
            BroadcastPlayListControl playListControl = (BroadcastPlayListControl) iPlayListControl;
            if (playListControl.hasNext()) {
                playListControl.getNextPlayItem(new IPlayListGetListener() {
                    @Override
                    public void onDataGet(PlayItem playItem, List<PlayItem> list) {
                        PlayerManagerHelper.getInstance().startPlayItemInList(playItem);
                    }

                    @Override
                    public void onDataGetError(PlayItem playItem, int i, int i1) {
                        PlayerManagerHelper.getInstance().seek(seekProgress);
                    }
                });
            } else {
                PlayerManagerHelper.getInstance().seek(seekProgress);
            }
        } else {
            PlayerManagerHelper.getInstance().seek(seekProgress);
        }
    }

    private BasePlayStateListener mPlayStateListener = new BasePlayStateListener() {
        @Override
        public void onIdle(PlayItem playItem) {
            BroadcastChannelAdapter.BroadcastChannelViewHolder currViewHolder = getViewHolder();
            if (currViewHolder != null) {
                currViewHolder.updateLivingPlayStatus();
            }
        }

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            onPlayChange();
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            isSeeking = false;
            bIsSeeking = false;
            /**
             * 解决 #36324
             */
            if (playItem == null) {
                return;
            }
            initSubscribe();
            if (mCurrentPlayItem == null || !mCurrentPlayItem.equals(playItem)) {
                mCurrentPlayItem = playItem;
                return;
            }
            scrollToPosition();
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            EventBus.getDefault().post(new PlayerPauseOrStartEBData());
        }

        @Override
        public void onProgress(PlayItem playItem, long progress, long duration) {
            if (isSeeking) {
                return;
            }
//            // 此逻辑目的是为屏蔽广播直播中的节目不走onProgress进度，需要参考此类中关于 onLivingCountDown 具体实现
//            if (PlayerManagerHelper.getInstance().isBroadcastPlayer()) {
//                PlayItem playItem = BroadcastRadioListManager.getInstance().getCurPlayItem();
//                if (playItem != null && playItem.isLivingUrl()) {
//                    return;
//                }
//            }
            Log.i(TAG, "playItem:" + playItem.getTitle() + " " + progress + "/" + duration);
            if (mBroadcastSeekBar != null && !bIsSeeking && progress != 0) {
                mBroadcastSeekBar.setProgress((int) progress);
                mBroadcastSeekBar.setElapsedTime(progress);
                mBroadcastSeekBar.setTotalTime(duration);
                mBroadcastSeekBar.setMaxProgress((int) duration);
            }
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int i, int i1) {
//          即便是订阅失败，也需要去查询是否订阅状态
            initSubscribe();
            ToastUtil.showOnly(AppDelegate.getInstance().getContext(), R.string.is_not_online);
        }


        @Override
        public void onSeekStart(PlayItem playItem) {
            isSeeking = true;
        }

        @Override
        public void onSeekComplete(PlayItem playItem) {
            isSeeking = false;
        }


        @Override
        public void onBufferingEnd(PlayItem playItem) {
            isSeeking = false;
        }
    };

    private void onPlayChange() {
        updatePlayerInfo();
        if (mDsvBroadcastChannels == null) {
            return;
        }
        BroadcastChannelAdapter adapter = (BroadcastChannelAdapter) mDsvBroadcastChannels.getAdapter();
        if (adapter == null) {
            return;
        }

        List<BroadcastRadioSimpleData> data = adapter.getDataList();
        if (ListUtil.isEmpty(data)) {
            return;
        }
        int curBroadcastPosition = PlayerManagerHelper.getInstance().getCurrentFrequencyPosition();
        if (curBroadcastPosition > -1) {
            try {
                if (mLayoutManager.getCurrentPosition() != curBroadcastPosition && !mItemClick) {
                    mDsvBroadcastChannels.smoothScrollToPosition(curBroadcastPosition);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            curBroadcastPosition = 0;
            adapter = new BroadcastChannelAdapter();
            mDsvBroadcastChannels.setAdapter(adapter);
            RecyclerViewExposeUtil exposeUtil = new RecyclerViewExposeUtil();
            exposeUtil.setRecyclerItemExposeListener(mDsvBroadcastChannels, this);
            adapter.setDataList(data);
        }

        BroadcastChannelAdapter.BroadcastChannelViewHolder currViewHolder = getViewHolder(curBroadcastPosition);
        if (currViewHolder != null) {
            currViewHolder.updateLivingPlayStatus();
        }
    }


//    private OnBroadcastRadioLivingListener mBroadcastRadioLivingListener = new OnBroadcastRadioLivingListener() {
//        @Override
//        public void onLivingCountDown(String timeStr) {
//            ProgramDetails item = BroadcastRadioListManager.getInstance().getCurRadioItem();
//            if (item == null) {
//                return;
//            }
//
//            String str = BroadcastRadioListManager.getInstance().getCurRadioItem().getBeginTime();
//            String[] strs = str.split(":");
//            int h = Integer.parseInt(strs[0]);
//            int m = Integer.parseInt(strs[1]);
//            int s = Integer.parseInt(strs[2]);
//
//            String[] hms = timeStr.split(":");
//            int hour = Integer.parseInt(hms[0]);
//            int minute = Integer.parseInt(hms[1]);
//            int second = Integer.parseInt(hms[2]);
//            int progress = (hour - h) * 60 * 60 + (minute - m) * 60 + (second - s);
//            progress *= 1000;
//            mBroadcastSeekBar.setElapsedTime(progress);
//            mBroadcastSeekBar.setProgress(progress);
//        }
//    };

    @Override
    public void onSubscribeSuccess(int status) {
        ToastUtil.showOnly(this, getString(R.string.subscribed_success_str));
        updateSubscribe(true);
    }

    @Override
    public void onSubscribeError() {
        ToastUtil.showOnly(this, getString(R.string.subscribe_failed_str));
    }

    @Override
    public void onUnSubscribeSuccess(int status) {
        ToastUtil.showOnly(this, getString(R.string.un_subscribed_success_str));
        updateSubscribe(false);
    }

    @Override
    public void onUnSubscribeError() {
        ToastUtil.showOnly(this, getString(R.string.un_subscribe_failed_str));
    }

    @Override
    public void updateSubscribe(boolean isSubscribe) {
        Log.i(TAG, "是否已经订阅: " + isSubscribe);
        mSubscribeView.setText(isSubscribe ? getString(R.string.subscribed_str) : getString(R.string.bradcast_subscribed_str));
        mSubscribeView.setSelected(!isSubscribe);
        mSubscribeView.setActivated(isSubscribe);
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        if (visible && mDsvBroadcastChannels != null) {
            BroadcastChannelAdapter adapter = (BroadcastChannelAdapter) mDsvBroadcastChannels.getAdapter();
            if (adapter != null) {
                BroadcastRadioSimpleData bean = adapter.getItemData(position);
                ReportUtil.addContentShowEvent("",
                        ReportParamUtil.getRadioType(ResType.BROADCAST_TYPE),
                        "0", String.valueOf(bean.getBroadcastId()),
                        "无", Constants.PAGE_ID_PLAYER_MUSIC_LIST, "", "");
            }

        }
    }

    private static class BroadcastListChangeListener implements IBroadcastListChangeListener {

        private WeakReference<BroadcastPlayerActivity> weakReference;

        public BroadcastListChangeListener(BroadcastPlayerActivity broadcastPlayerFragment) {
            weakReference = new WeakReference<>(broadcastPlayerFragment);
        }

        @Override
        public void onBroadcastListChanged(ArrayList<BroadcastRadioSimpleData> broadcastRadioSimpleDataArrayList) {
            BroadcastPlayerActivity broadcastPlayerFragment = weakReference.get();
            if (broadcastPlayerFragment != null) {
                broadcastPlayerFragment.manageBroadcastListChanged(broadcastRadioSimpleDataArrayList);
            }
        }

        @Override
        public void onPlayItemChange(int position) {
            BroadcastPlayerActivity broadcastPlayerFragment = weakReference.get();

            if (broadcastPlayerFragment != null) {
                broadcastPlayerFragment.initSubscribe();
                broadcastPlayerFragment.manageOnPlayerListChanged();
                broadcastPlayerFragment.onPlayChange();
                if (broadcastPlayerFragment.isNeedRefresh) {
                    broadcastPlayerFragment.isNeedRefresh = false;
                    broadcastPlayerFragment.initViewInner();
                    broadcastPlayerFragment.mRootLayout.postDelayed(() -> {
                        ScaleLayoutManager scaleLayoutManager = broadcastPlayerFragment.mLayoutManager;
                        if (scaleLayoutManager != null) {
                            scaleLayoutManager.scrollToPosition(position);
                        }
                    }, 100);
                } else {
                    broadcastPlayerFragment.scrollToPosition();
                }
            }
        }
    }


    private void scrollToPosition() {
        int curBroadcastPosition = PlayerManagerHelper.getInstance().getCurrentFrequencyPosition();
        try {
            if (mLayoutManager.getCurrentPosition() != curBroadcastPosition) {
                mDsvBroadcastChannels.smoothScrollToPosition(curBroadcastPosition);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static class BroadcastIPlayerListChangedListener implements IPlayListStateListener {

        private WeakReference<BroadcastPlayerActivity> weakReference;

        public BroadcastIPlayerListChangedListener(BroadcastPlayerActivity broadcastPlayerFragment) {
            weakReference = new WeakReference<>(broadcastPlayerFragment);
        }

        @Override
        public void onPlayListChange(List<PlayItem> arrayList) {
            BroadcastPlayerActivity broadcastPlayerFragment = weakReference.get();
            if (broadcastPlayerFragment != null) {
                broadcastPlayerFragment.initSubscribe();
                broadcastPlayerFragment.manageOnPlayerListChanged();
                // fragment改为activity后不存在这种情况
//                SwitchPlayerHelper.getInstance().changePlayer(broadcastPlayerFragment, PlayerConstants.RESOURCES_TYPE_BROADCAST);
            }
        }

        @Override
        public void onPlayListChangeError(PlayItem playItem, int i, int i1) {

        }
    }

    private void manageBroadcastListChanged(ArrayList<BroadcastRadioSimpleData> broadcastRadioSimpleDatas) {
        BroadcastChannelAdapter adapter = (BroadcastChannelAdapter) mDsvBroadcastChannels.getAdapter();
        if (adapter == null) {
            adapter = new BroadcastChannelAdapter();
            mDsvBroadcastChannels.setAdapter(adapter);
            RecyclerViewExposeUtil exposeUtil = new RecyclerViewExposeUtil();
            exposeUtil.setRecyclerItemExposeListener(mDsvBroadcastChannels, this);
        }
        adapter.setDataList(broadcastRadioSimpleDatas);
    }

    private void manageOnPlayerListChanged() {
        Log.i(TAG, "onPlayerListChanged");
        if (!isHasBroadcastPlayList()) {
            updatePlayListBtnEnabled(false);
            mBroadcastSeekBar.setElapsedTime(-1);
            mBroadcastSeekBar.setTotalTime(-1);
            hideEmptyPlaylist();
        } else {
            updatePlayListBtnEnabled(true);
        }
        if (mPlayListView != null && mPlayListView.isShow()) {
            updatePlayListView();
        }
    }

    public class BroadcastChannelAdapter extends BaseAdapter<BroadcastRadioSimpleData> {

        @Override
        protected BaseHolder<BroadcastRadioSimpleData> getViewHolder(ViewGroup parent, int viewType) {
            return new BroadcastChannelViewHolder(inflate(parent, R.layout.broadcast_channel_item, viewType));
        }

        public class BroadcastChannelViewHolder extends BaseHolder<BroadcastRadioSimpleData> {

            TextView mTvBroadcastName;
            TextView mTvBroadcastFreq;
            TextView mTvBroadcastType;
            ImageView ivLogo;
            View llFreq;

            View mItemView;

            public BroadcastChannelViewHolder(View itemView) {
                super(itemView);
                mItemView = itemView;
                mTvBroadcastName=itemView.findViewById(R.id.tv_broadcast_name);
                mTvBroadcastFreq=itemView.findViewById(R.id.tv_broadcast_freq);
                mTvBroadcastType=itemView.findViewById(R.id.tv_broadcast_type);
                ivLogo=itemView.findViewById(R.id.ivLogo);
                llFreq=itemView.findViewById(R.id.llFreq);
            }

            @Override
            public void setupData(BroadcastRadioSimpleData broadcastRadioSimpleData, int position) {
                String title = broadcastRadioSimpleData.getName();
                if (TextUtils.isEmpty(title)) {
                    return;
                }
                ImageLoader.getInstance().displayImage(BroadcastPlayerActivity.this, broadcastRadioSimpleData.getImg(), ivLogo);
                updateLivingPlayStatus();

                String[] name = title.split("  ");
                mTvBroadcastName.setText(name[0]);
                llFreq.setVisibility(View.GONE);
                if (name.length > 1) {
                    String freq = name[1];
                    if (!TextUtils.isEmpty(freq)) {
                        mTvBroadcastFreq.setText(freq.replace("FM", ""));
                        llFreq.setVisibility(View.VISIBLE);
                    }
                }
                boolean isSelected = false;

                int tempPosition = PlayerManagerHelper.getInstance().getCurrentFrequencyPosition();
                if (tempPosition != -1) {
                    isSelected = position == tempPosition;
                } else {
                    PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                    if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_BROADCAST) {
                        isSelected = ((BroadcastPlayItem) playItem).getInfoData().getAlbumId() == broadcastRadioSimpleData.getBroadcastId();
                        String freq = ((BroadcastPlayItem) playItem).getFrequencyChannel();
                        if (!TextUtils.isEmpty(freq)) {
                            mTvBroadcastFreq.setText(freq.replace("FM", ""));
                            llFreq.setVisibility(View.VISIBLE);
                        }
                    }
                }

                changeItemCheckedStatus(isSelected);
            }

            public void changeItemCheckedStatus(boolean isSelected) {
                //mItemView.setBackgroundResource(contains ? R.drawable.broadcast_item_bg_checked : R.drawable.broadcast_item_bg_checked);
//                mTvBroadcastName.setTextColor(ResUtil.getColor(isSelected ? R.color.tv_broadcast_freq_playing_color : R.color.tv_broadcast_freq_normal_color));
//                mTvBroadcastFreq.setTextColor(ResUtil.getColor(isSelected ? R.color.tv_broadcast_name_playing_color : R.color.tv_broadcast_name_normal_color));
                mTvBroadcastType.setVisibility(isSelected ? View.VISIBLE : View.GONE);
                /**
                 * 解决 #36635
                 */
            }

            public void updateLivingPlayStatus() {
                PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                if (playItem instanceof BroadcastPlayItem) {
                    if (((BroadcastPlayItem) playItem).getStatus() == BROADCAST_STATUS_PLAYBACK) {
                        mTvBroadcastType.setText(R.string.broadcast_playBack_str);
//                        mTvBroadcastType.setBackgroundResource(R.drawable.flag_playback_bg);
                        return;
                    }
                }
                mTvBroadcastType.setText(R.string.broadcast_living_str2);
//                mTvBroadcastType.setBackgroundResource(R.drawable.flag_live_bg);
            }
        }
    }

    private View.OnClickListener playListClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if (AntiShake.check(v.getId())) {
                return;
            }
            if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(BroadcastPlayerActivity.this)) {
                return;
            }
            updatePlayListBtnEnabled(isHasBroadcastPlayList());
            boolean isActivated = !playList.isActivated();
            if (isActivated) {
                isShowSubscribeView(false);
                playList.setActivated(true);
                onPlayListShow();
                ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PLAY_LIST, PlayerUiControlReportEvent.CONTROL_TYPE_CLICK, PlayerUiControlReportEvent.POSITION_PLAY_FRAGEMNT);
            } else {
                if (mPlayListView.isShown()) {
                    isShowSubscribeView(true);
                    playList.setActivated(false);
                    onPlayListHide();
                    ReportUtil.setPageId(Constants.PAGE_ID_BROADCAST_MAIN);
                }
            }
            mPlayerFragmentHelper.switchPlayListShowOrHide(mPlayListView, isActivated);
            playList.setEnabled(!isActivated);
            updatePrevNextBtn();
        }
    };

    private void initPlayListBtnView() {
        playList.setOnClickListener(playListClickListener);
    }

    private void onPlayListShow() {
        //        boolean canPicConfig = PerformanceSettingMananger.getInstance().isShowConfig();
//        if (canPicConfig) {
//        boolean isNeedMongolia = PerformanceSettingMananger.getInstance().getPlayerIsNeedMongolia();
//            ViewUtil.setViewVisibility(mLayerMeng, isNeedMongolia ? View.VISIBLE : View.GONE);
//        } else {
//            ViewUtil.setViewVisibility(mLayerMeng, View.VISIBLE);
//        }
        ViewUtil.setViewVisibility(mBroadcastShowHideGroup, View.GONE);
//        if (Configuration.ORIENTATION_PORTRAIT == ResUtil.getOrientation()) {
//            ViewUtil.setViewVisibility(mBroadcastSeekBar, View.VISIBLE);
//        } else {
//            ViewUtil.setViewVisibility(mBroadcastSeekBar, View.GONE);
//        }
    }

    private void onPlayListHide() {
//        ViewUtil.setViewVisibility(mLayerMeng, View.GONE);
        ViewUtil.setViewVisibility(mBroadcastShowHideGroup, View.VISIBLE);
        ViewUtil.setViewVisibility(mBroadcastSeekBar, View.VISIBLE);
    }

    public boolean isPlayListShow() {
        if (mPlayListView != null && mPlayListView.getVisibility() == View.VISIBLE) {
            onPlayListBackClick(false);
            return true;
        }
        return false;
    }

    public void onPlayListBackClick(boolean isShow) {
        updatePlayListBtnEnabled(isHasBroadcastPlayList());
        if (playList != null) {
            playList.setActivated(false);
        }
        onPlayListHide();
        ReportUtil.setPageId(Constants.PAGE_ID_BROADCAST_MAIN);
        mPlayerFragmentHelper.switchPlayListShowOrHide(mPlayListView, isShow);
        playList.setEnabled(true);
        isShowSubscribeView(!isShow);
        updatePrevNextBtn();
    }


    //   是否显示订阅按钮
    private void isShowSubscribeView(boolean isShow) {
        if (isShow) {
            mSubscribeView.setVisibility(View.VISIBLE);
        } else {
            mSubscribeView.setVisibility(View.INVISIBLE);
        }
    }

    private BroadcastChannelAdapter.BroadcastChannelViewHolder getViewHolder() {
        if (mLayoutManager != null) {
            return getViewHolder(mLayoutManager.getCurrentPosition());
        }
        return null;
    }

    private BroadcastChannelAdapter.BroadcastChannelViewHolder getViewHolder(int position) {
        if (mDsvBroadcastChannels != null) {
            return (BroadcastChannelAdapter.BroadcastChannelViewHolder) mDsvBroadcastChannels.findViewHolderForLayoutPosition(position);
        }
        return null;
    }


    public boolean onBackPressedSupports() {
        return isPlayListShow();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (ResUtil.getOrientation() == Configuration.ORIENTATION_LANDSCAPE) {
            uploadView(true);
        } else {
            uploadView(false);
        }
    }

    @Override
    public void onMultiWindowModeChanged(boolean isInMultiWindowMode) {
        super.onMultiWindowModeChanged(isInMultiWindowMode);
        Log.d(TAG, "onMultiWindowModeChanged " + isInMultiWindowMode);
        final int curPosition = mLayoutManager.getCurrentPosition();
        RecyclerViewUtils.postResetRecyclerView(mDsvBroadcastChannels);
        mDsvBroadcastChannels.postDelayed(() -> {
            if (mLayoutManager != null) {
                mLayoutManager.scrollToPosition(curPosition);
            }
        }, 100);
    }

    private void uploadView(boolean isLand) {
        ConstraintSet set = new ConstraintSet();
        set.clone(mRootLayout);
        if (isLand) {
            set.setVerticalBias(mPlayerTitleLine.getId(), ViewConstants.TITLE_LAND_PERCENT);
            set.constrainPercentWidth(mBroadcastSeekBar.getId(), 0.6f);
            set.constrainPercentWidth(mPlayerControllerBar.getId(), 0.54f);
//            set.constrainPercentHeight(mPlayerControllerBar.getId(), 0.16f);
//            set.setVerticalBias(mPlayerControllerBar.getId(), 0.98f);
//            set.constrainPercentHeight(mLayerMeng.getId(), 0.426f);
            if (mKRadioMultiWindowInter != null && mKRadioMultiWindowInter.getMultiStatus(this)) {
                mKRadioMultiWindowInter.doMultiBroadcastPlayerFragmentList(this, set);
            } else {
//                set.constrainPercentWidth(mPlayListView.getId(), 0.8f);
                set.setMargin(mPlayListView.getId(), 6, ResUtil.getDimen(R.dimen.x80));
                set.setMargin(mPlayListView.getId(), 7, ResUtil.getDimen(R.dimen.x80));
            }
            set.setVerticalBias(mDsvBroadcastChannels.getId(), 0.21f);
            mPlayListView.configurationChanged(true);
            set.setMargin(mPlayerControllerBar.getId(), 4, ResUtil.getDimen(R.dimen.y45));
        } else {
            set.setVerticalBias(mPlayerTitleLine.getId(), ViewConstants.TITLE_PORT_PERCENT);
            set.constrainPercentWidth(mBroadcastSeekBar.getId(), 0.9f);
            set.constrainPercentWidth(mPlayerControllerBar.getId(), 0.84f);
            if (mKRadioMultiWindowInter != null && mKRadioMultiWindowInter.getMultiStatus(this)) {
                mKRadioMultiWindowInter.doMultiBroadcastPlayerFragmentPlayIcon(this, set);
            } else {
//                set.constrainPercentHeight(mPlayerControllerBar.getId(), 0.10f);
//                set.setVerticalBias(mPlayerControllerBar.getId(), 0.85f);
            }
//            set.constrainPercentHeight(mLayerMeng.getId(), 0.6f);
//            set.constrainPercentWidth(mPlayListView.getId(), 1);
            set.setMargin(mPlayListView.getId(), 6, ResUtil.getDimen(R.dimen.x50));
            set.setMargin(mPlayListView.getId(), 7, ResUtil.getDimen(R.dimen.x50));
            set.setVerticalBias(mDsvBroadcastChannels.getId(), 0.3f);
            mPlayListView.configurationChanged(false);
            set.setMargin(mPlayerControllerBar.getId(), 4, ResUtil.getDimen(R.dimen.y75));
        }
        set.applyTo(mRootLayout);
//        if (isLand) {
//            if (playList.isActivated()) {
//                ViewUtil.setViewVisibility(mBroadcastSeekBar, View.GONE);
//            }
//        } else {
//            ViewUtil.setViewVisibility(mBroadcastSeekBar, View.VISIBLE);
//        }
        final int curPosition = mLayoutManager.getCurrentPosition();
        RecyclerViewUtils.postResetRecyclerView(mDsvBroadcastChannels);
        mDsvBroadcastChannels.postDelayed(() -> {
            if (mLayoutManager != null) {
                mLayoutManager.scrollToPosition(curPosition);
            }
        }, 100);
    }

    private void initViewInner() {
        int mCurrentOrientation = ResUtil.getOrientation();
        if (mCurrentOrientation == Configuration.ORIENTATION_PORTRAIT) {
            uploadView(false);
        } else if (mCurrentOrientation == Configuration.ORIENTATION_LANDSCAPE) {
            uploadView(true);
        }
    }

    /**
     * 更新播单按钮是否可以点击
     *
     * @param isEnable
     */
    private void updatePlayListBtnEnabled(boolean isEnable) {
//        ViewUtil.setEnabled(playList, isEnable);
        if (!isEnable) {
            playList.setActivated(true);
            playList.setOnClickListener(v -> {
                ToastUtil.showOnly(v.getContext(), R.string.broadcast_no_program);
            });
        } else {
            playList.setActivated(false);
            playList.setOnClickListener(playListClickListener);
        }
    }

    /**
     * 隐藏
     */
    private void hideEmptyPlaylist() {
        if (mPlayListView.isShow()) {
            playList.setActivated(false);
            onPlayListHide();
            mPlayerFragmentHelper.switchPlayListShowOrHide(mPlayListView, false);
            playList.setEnabled(true);
            isShowSubscribeView(true);
            updatePrevNextBtn();
        }
    }

    private void updatePrevNextBtn() {
        if (playList.isEnabled()) {
            broadcast_play_next_audio.updateState(PlayerManagerHelper.getInstance().hasNextItem());
            broadcast_play_pre_audio.updateState(PlayerManagerHelper.getInstance().hasPreItem());
        } else {
            broadcast_play_next_audio.updateState(PlayerManagerHelper.getInstance().hasNextProgramItem()
                    && !PlayerManagerHelper.getInstance().isNotStartProgram(PlayerManagerHelper.getInstance().getNextPlayItem()));
            broadcast_play_pre_audio.updateState(PlayerManagerHelper.getInstance().hasPreProgramItem());
        }

    }

    /**
     * 检查是否有广播播单
     *
     * @return
     */
    private boolean isHasBroadcastPlayList() {
        boolean isHasPlayList = true;
        List<PlayItem> playItemList = PlayerManager.getInstance().getPlayList();
        if (ListUtil.isEmpty(playItemList)) {
            return false;
        }
        if (PlayerManagerHelper.getInstance().isBroadcastPlayer() && !ListUtil.isEmpty(playItemList)) {
            PlayItem playItem = playItemList.get(0);
            if (playItem.getAudioId() == 0) {
                return false;
            }
        }
        return isHasPlayList;
    }

    private void updatePlayListView() {
        if (mPlayListView != null) {
            mPlayListView.updateBroadcastList();
        }
    }

    boolean isNeedRefresh = false;

    @Override
    public void onPause() {
        super.onPause();
        //isNeedRefresh = true;
    }

    private IGeneralListener mGeneralListener = new IGeneralListener() {

        @Override
        public void getPlayListError(PlayItem playItem, int i, int i1) {
            if (isPlayError()) {
                onPlayChange();
                updatePlayListBtnEnabled(isHasBroadcastPlayList());
                manageOnPlayerListChanged();
            }
        }

        @Override
        public void playUrlError(int code) {

        }
    };

    public boolean isReportFragment() {
        return true;
    }
}
