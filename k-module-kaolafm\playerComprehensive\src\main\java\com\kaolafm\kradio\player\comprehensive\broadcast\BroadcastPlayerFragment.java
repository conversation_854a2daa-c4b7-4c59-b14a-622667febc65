package com.kaolafm.kradio.player.comprehensive.broadcast;

import android.app.Activity;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.bumptech.glide.Glide;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.common.event.PlayerCheckPreOrNextButtonEBData;
import com.kaolafm.kradio.common.event.PlayerPauseOrStartEBData;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.base.mvp.BasePresenter;
import com.kaolafm.kradio.lib.base.ui.BaseActivity;
import com.kaolafm.kradio.lib.base.ui.BaseShowHideFragment;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.player.comprehensive.play.widget.BroadcastCitySpinner;
import com.kaolafm.kradio.player.comprehensive.play.widget.BroadcastPlayListContent;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.api.broadcast.BroadcastAreaInfo;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IGeneralListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.model.PlaylistInfo;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TVPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.ref.WeakReference;
import java.util.List;

/**
 * Created by kaolafm on 2018/10/16.
 */
@Route(path = RouterConstance.PLAY_BROADCAST_COMPREHENSIVE_URL)
public class BroadcastPlayerFragment extends BaseShowHideFragment<BasePresenter>
        implements RecyclerViewExposeUtil.OnItemExposeListener {
    private static final String TAG = "BroadcastPlayerFragment";

    View backView;
    TextView broadcastPlayerHorizontalTitleText;
    TextView broadcastPlayerHorizontalFreqText;
    BroadcastPlayListContent mPlayListView;
    BroadcastCitySpinner broadcastCitySpinner;
    ImageView broadcastCoverImage;
    ImageView broadcastLivingTag;
    ViewStub broadcastNoPlaylistViewstub;

    int mReflectionStartColor;
    int mReflectionEndColor;
    ViewStub playerBarViewStub;

    private PlayItem mCurrentPlayItem;

    private IPlayListStateListener mBroadcastIPlayerListChangedListener;

    private static final float REFLECT_VALUE = 0.130F;
    private View noPlayListLayout;  //没有播单时显示的View
    //无播单或者网络错误时的图片承载组件
    private ImageView broadcastNoPlaylistPic;
    //无播单或者网络错误时的提示信息承载组件
    private TextView broadcastNoPlaylistMessage;

    private boolean isShowWindow = false;
    private Activity activity;
    private boolean hasPlayerBar;//是否显示播放器
    private ImageView broadcastTitleImage;

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        this.activity = activity;
    }

    @Override
    public void initArgs() {
        super.initArgs();
        Bundle arguments = getArguments();
        if (arguments != null) {
            hasPlayerBar = arguments.getBoolean("hasPlayerBar", false);
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (mCurrentPlayItem == null || !PlayerManagerHelper.getInstance().isSameProgram(mCurrentPlayItem, playItem)) {
            mCurrentPlayItem = playItem;
        }
        updatePlayerInfo();
        initListener();
    }

    private void initListener() {
        backView.setOnClickListener(v -> {
            if (AntiShake.check(v.getId())) {
                return;
            }
            if (activity instanceof BaseActivity) {
                ((BaseActivity<?>) activity).onBackPressedSupport();
            } else {
                if (!onBackPressedSupport()) {
                    pop();
                }
            }

        });
        mBroadcastIPlayerListChangedListener = new BroadcastIPlayerListChangedListener(this);
        PlayerManager.getInstance().addGeneralListener(mGeneralListener);
        PlayerManager.getInstance().addPlayListControlStateCallback(mBroadcastIPlayerListChangedListener);
    }

    /**
     * 更新错误的播放信息, 主要用于, 播单获取失败, 又要强行设置要播放的信息.
     */
    private void updateErrorPlayerInfo() {
        Log.i(TAG, "更新错误播单信息");
    }

//    private boolean isPlayError() {
//        PlayItem playItemTemp = PlayerManager.getInstance().getCurPlayItem();
//        if (playItemTemp.getType() != PlayerConstants.RESOURCES_TYPE_INVALID) {
//            return false;
//        }
//        return PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_BROADCAST &&
//                PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_TV;
//    }

    private void showRadioCover() {
        if (mCurrentPlayItem == null) return;
        String coverUrl = PlayerManagerHelper.getInstance().getPlayItemPicUrl(mCurrentPlayItem);//节目图片
        String columUrl = null;
        if (TextUtils.isEmpty(coverUrl)) {
            if (mCurrentPlayItem instanceof BroadcastPlayItem) {
                coverUrl = ((BroadcastPlayItem) mCurrentPlayItem).getInfoData().getImage();//节目图片
                columUrl = ((BroadcastPlayItem) mCurrentPlayItem).getInfoData().getAlbumPic();//栏目图
            }
            if (mCurrentPlayItem instanceof TVPlayItem){
                coverUrl = ((TVPlayItem) mCurrentPlayItem).getInfoData().getImage();//节目图片
                columUrl = ((TVPlayItem) mCurrentPlayItem).getInfoData().getAlbumPic();//栏目图
            }
        }
        Glide.with(this).load(coverUrl).into(broadcastCoverImage);
        Glide.with(this).load(columUrl).into(broadcastTitleImage);
    }


    private void updatePlayerInfo() {
//        if (isPlayError()) {
//            updateErrorPlayerInfo();
//            return;
//        }

        showRadioCover();

        PlayItem playItemTemp = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItemTemp == null) {
            return;
        }
        if (playItemTemp.getType() == PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            BroadcastPlayItem playItem = (BroadcastPlayItem) playItemTemp;
            if (broadcastPlayerHorizontalTitleText != null) {
                String title;
                String freqChannel = playItem.getFrequencyChannel();
                if (TextUtils.isEmpty(freqChannel)) {
                    title = playItem.getAlbumTitle();
                    broadcastPlayerHorizontalFreqText.setVisibility(View.GONE);
                    broadcastPlayerHorizontalTitleText.setText(StringUtil.getMaxString(title, 20));
                } else {
                    broadcastPlayerHorizontalFreqText.setVisibility(View.VISIBLE);
                    broadcastPlayerHorizontalFreqText.setText(freqChannel);
                    String name = playItem.getAlbumTitle();
                    broadcastPlayerHorizontalTitleText.setText(StringUtil.getMaxString(name, 20));
                }
            }
        } else if (playItemTemp.getType() == PlayerConstants.RESOURCES_TYPE_TV) {
            TVPlayItem playItem = (TVPlayItem) playItemTemp;
            if (broadcastPlayerHorizontalTitleText != null) {
                String title;
                String freqChannel = playItem.getFrequencyChannel();
                if (TextUtils.isEmpty(freqChannel)) {
                    title = playItem.getAlbumTitle();
                    broadcastPlayerHorizontalFreqText.setVisibility(View.GONE);
                    broadcastPlayerHorizontalTitleText.setText(StringUtil.getMaxString(title, 20));
                } else {
                    broadcastPlayerHorizontalFreqText.setVisibility(View.VISIBLE);
                    broadcastPlayerHorizontalFreqText.setText(freqChannel);
                    String name = playItem.getAlbumTitle();
                    broadcastPlayerHorizontalTitleText.setText(StringUtil.getMaxString(name, 20));
                }
            }
        }

    }


    @Override
    public void initView(View view) {
        backView=view.findViewById(R.id.backView);
        broadcastTitleImage=view.findViewById(R.id.broadcastTitleImage);
        broadcastPlayerHorizontalTitleText=view.findViewById(R.id.broadcast_player_horizontal_title_text);
        broadcastPlayerHorizontalFreqText=view.findViewById(R.id.broadcast_player_horizontal_freq_text);
        mPlayListView=view.findViewById(R.id.broadcast_player_horizontal_play_list);
        broadcastCitySpinner=view.findViewById(R.id.location_select_layout);
        broadcastCoverImage=view.findViewById(R.id.broadcastCoverImage);
        broadcastLivingTag=view.findViewById(R.id.broadcast_living_tag);
        broadcastNoPlaylistViewstub=view.findViewById(R.id.broadcast_no_playlist_viewstub);
        playerBarViewStub=view.findViewById(R.id.playerBarViewStub);

        mReflectionStartColor= getContext().getResources().getColor(R.color.color_cover_reflection_start);
        mReflectionEndColor= getContext().getResources().getColor(R.color.color_cover_reflection_end);

        PlayerManagerHelper.getInstance().setInProgramPlayerPage(true);
        initViewInner();
        mPlayListView.setFragmentManager(getChildFragmentManager());
        mPlayListView.setHttpCallback(new BroadcastPlayListContent.HttpCallback() {
            @Override
            public void onHttpError() {
                onPlayListHide(true);
            }

            @Override
            public void onHttpSucceed() {
                showOrHidePlaylist();
            }
        });

        showOrHidePlaylist();
        broadcastCitySpinner.setPageId(getPageId());
        broadcastCitySpinner.setOnExpandListener(() -> {

        });
        updateCityAreas();

    }

    /**
     * 根据广播详情切换播单状态
     */
    private void showOrHidePlaylist() {
        if (!NetworkUtil.isNetworkAvailable(getContext())) {
            onPlayListHide(true);
        } else if (PlayerManagerHelper.getInstance().isHasBroadcastPlayList()) {
            onPlayListShow();
        } else onPlayListHide(false);
        EventBus.getDefault().post(new PlayerCheckPreOrNextButtonEBData());

        // 注册EventBus监听主题变化
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        PlayerManagerHelper.getInstance().setInProgramPlayerPage(false);
        EventBus.getDefault().post(new PlayerCheckPreOrNextButtonEBData());

        // 注销EventBus
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }


    @Override
    protected int getLayoutId() {
        return R.layout.comprehensive_fragment__broadcast_player;
    }

    @Override
    protected int getLayoutId_Tow() {
        return R.layout.comprehensive_fragment__broadcast_player_1280_720;
    }

    @Override
    protected BasePresenter createPresenter() {
        return null;
    }

    @Override
    public void onUserVisible() {
        super.onUserVisible();
        PlayerManager.getInstance().addPlayControlStateCallback(mPlayStateListener);
    }

    @Override
    public void onUserInvisible() {
        PlayerManager.getInstance().removePlayControlStateCallback(mPlayStateListener);
        super.onUserInvisible();
    }

    @Override
    public void onDestroy() {
        Log.i("BroadcastPlayerFragment", "onDestroy");
        if (mPlayListView != null) {
            mPlayListView.onDestroy();
        }
        super.onDestroy();

        PlayerManager.getInstance().removeGeneralListener(mGeneralListener);
        PlayerManager.getInstance().removePlayListControlStateCallback(mBroadcastIPlayerListChangedListener);

        if (mPlayListView != null) {
            mPlayListView.onDestroy();
        }

    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_PLAYER_MUSIC_LIST;
    }


    private final BasePlayStateListener mPlayStateListener = new BasePlayStateListener() {
        @Override
        public void onIdle(PlayItem playItem) {
        }

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            if (playItem == null) {
                return;
            }

            if (mCurrentPlayItem == null || !PlayerManagerHelper.getInstance().isSameProgram(mCurrentPlayItem, playItem)) {
                mCurrentPlayItem = playItem;
            }
            onPlayChange();
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            if (playItem == null) {
                return;
            }

            if (mCurrentPlayItem == null || !PlayerManagerHelper.getInstance().isSameProgram(mCurrentPlayItem, playItem)) {
                mCurrentPlayItem = playItem;
            }
            onPlayChange();
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            EventBus.getDefault().post(new PlayerPauseOrStartEBData());
        }

        @Override
        public void onProgress(PlayItem playItem, long progress, long duration) {
        }
    };

    private void onPlayChange() {
        updatePlayerInfo();
        showOrHidePlaylist();
        updateCityAreas();
    }

    private void updateCityAreas() {
        boolean isBroadcastPlayer = PlayerManagerHelper.getInstance().isBroadcastPlayer();
        Log.i(TAG, "updateCityAreas() --- isBroadcastPlayer = " + isBroadcastPlayer);
        if (!isBroadcastPlayer) return;
        PlaylistInfo playListInfo = PlayerManager.getInstance().getPlayListInfo();
        Log.i(TAG, "updateCityAreas() --- playListInfo.getBroadcastMultiAreaList().size = " + (playListInfo == null ? 0 : (playListInfo.getBroadcastMultiAreaList() == null ? 0 : playListInfo.getBroadcastMultiAreaList().size())));
        if (playListInfo == null || ListUtil.isEmpty(playListInfo.getBroadcastMultiAreaList())) {
            ViewUtil.setViewVisibility(broadcastCitySpinner, View.GONE);
            return;
        }
        ViewUtil.setViewVisibility(broadcastCitySpinner, View.VISIBLE);
        broadcastCitySpinner.setData(playListInfo.getBroadcastMultiAreaList());
        String areaName = playListInfo.getAreaName();
        Log.i(TAG, "updateCityAreas() --- areaName = " + areaName);

        // 省份为空时默认显示北京
        if (TextUtils.isEmpty(areaName) && TextUtils.equals("0", playListInfo.getAreaCode())){
            String beiJingCode = "";
            for (BroadcastAreaInfo areaInfo : playListInfo.getBroadcastMultiAreaList()) {
                if (TextUtils.equals("北京", areaInfo.getAreaName())){
                    beiJingCode = areaInfo.getAreaCode();
                    break;
                }
            }
            playListInfo.setAreaName("北京");
            playListInfo.setAreaCode(beiJingCode);
            areaName = "北京";
        }

        if (StringUtil.isNotEmpty(areaName)) {
            broadcastCitySpinner.setText(areaName);
            broadcastCitySpinner.setContentDescription(areaName);
        }
        broadcastCitySpinner.setCurrentAreaCode(playListInfo.getAreaCode());

        PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();

        if (curPlayItem instanceof BroadcastPlayItem) {
            if (StringUtil.isNotEmpty(((BroadcastPlayItem) curPlayItem).getFrequencyChannel())) {
                broadcastPlayerHorizontalFreqText.setText(((BroadcastPlayItem) curPlayItem).getFrequencyChannel());
                ViewUtil.setViewVisibility(broadcastPlayerHorizontalFreqText, View.VISIBLE);
            }
            broadcastPlayerHorizontalTitleText.setText(StringUtil.getMaxString(curPlayItem.getAlbumTitle(), 20));

        } else {
            ViewUtil.setViewVisibility(broadcastPlayerHorizontalFreqText, View.GONE);
        }

        String radioId = null;
        if (curPlayItem != null) radioId = curPlayItem.getRadioId();
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_BROADCAST_AREA_LABEL, areaName, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN, null, null, null, radioId));
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {

    }

    private static class BroadcastIPlayerListChangedListener implements IPlayListStateListener {

        private final WeakReference<BroadcastPlayerFragment> weakReference;

        public BroadcastIPlayerListChangedListener(BroadcastPlayerFragment broadcastPlayerFragment) {
            weakReference = new WeakReference<>(broadcastPlayerFragment);
        }

        @Override
        public void onPlayListChange(List<com.kaolafm.opensdk.player.logic.model.item.model.PlayItem> arrayList) {
            BroadcastPlayerFragment broadcastPlayerFragment = weakReference.get();
            if (broadcastPlayerFragment != null) {
                broadcastPlayerFragment.manageOnPlayerListChanged();
            }
        }

        @Override
        public void onPlayListChangeError(PlayItem playItem, int i, int i1) {
            Log.d(TAG, "onPlayListChangeError");
        }

    }

    private int getCurrentType() {
        if (mCurrentPlayItem == null) return PlayerConstants.RESOURCES_TYPE_BROADCAST;
        return mCurrentPlayItem.getType();
    }

    private void manageOnPlayerListChanged() {
        Log.i(TAG, "onPlayerListChanged");
        if (mPlayListView != null) {
            updatePlayListView();
        }
    }

    private void onPlayListShow() {
        if (mPlayListView.getVisibility() == View.VISIBLE) {
            return;
        }
        ViewUtil.setViewVisibility(mPlayListView, View.VISIBLE);
        if (noPlayListLayout != null) {
            ViewUtil.setViewVisibility(noPlayListLayout, View.GONE);
        }
        mPlayListView.showPlayList();
    }

    private void onPlayListHide(boolean isNetError) {
        ViewUtil.setViewVisibility(mPlayListView, View.GONE);
        if (broadcastNoPlaylistViewstub != null) {
            noPlayListLayout = broadcastNoPlaylistViewstub.inflate();
            broadcastNoPlaylistViewstub = null;
            broadcastNoPlaylistPic = noPlayListLayout.findViewById(R.id.broadcast_no_playlist_pic);
            broadcastNoPlaylistMessage = noPlayListLayout.findViewById(R.id.broadcast_no_playlist_message);
        } else if (noPlayListLayout != null) {
            ViewUtil.setViewVisibility(noPlayListLayout, View.VISIBLE);
        }
        if (isNetError) {
            broadcastNoPlaylistPic.setImageDrawable(ResUtil.getDrawable(R.drawable.ic_network_error));
            broadcastNoPlaylistMessage.setText(ResUtil.getString(R.string.network_error_toast_no_exclamation_point));
            noPlayListLayout.setContentDescription(ResUtil.getString(R.string.content_desc_refresh_no_network));
            noPlayListLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    showOrHidePlaylist();
                }
            });
        } else {
            broadcastNoPlaylistPic.setImageDrawable(ResUtil.getDrawable(R.drawable.comprehensive_player_broadcast_no_playlist));
            broadcastNoPlaylistMessage.setText(ResUtil.getString(R.string.comprehensive_player_broadcast_no_playlist));
            noPlayListLayout.setContentDescription(null);
            noPlayListLayout.setOnClickListener(null);
            noPlayListLayout.setClickable(false);
        }
    }

    public void onPlayListBackClick(boolean isShow) {
        onPlayListHide(false);
        ReportUtil.setPageId(Constants.PAGE_ID_BROADCAST_MAIN);
    }


    @Override
    public void onResume() {
        super.onResume();
        if (isShowWindow) {
            isShowWindow = false;
            broadcastCitySpinner.expand();
        }
    }

    @Override
    public void onMultiWindowModeChanged(boolean isInMultiWindowMode) {
        super.onMultiWindowModeChanged(isInMultiWindowMode);
        Log.d(TAG, "onMultiWindowModeChanged " + isInMultiWindowMode);
    }

    private void uploadView(boolean isLand) {
        if (isLand) {
            if (BuildConfig.LAYOUT_TYPE == 0)
                broadcastCitySpinner.setPopupWidth(ResUtil.getDimen(R.dimen.comprehensive_player_broadcast_city_popup_width));
            else
                broadcastCitySpinner.setPopupWidth(ResUtil.getDimen(R.dimen.comprehensive_player_broadcast_city_popup_width_1280_720));
        }

        if (hasPlayerBar && playerBarViewStub != null) {
            playerBarViewStub.inflate();
            playerBarViewStub = null;
        }
    }

    private void initViewInner() {
        int mCurrentOrientation = ResUtil.getOrientation();
        if (mCurrentOrientation == Configuration.ORIENTATION_PORTRAIT) {
            uploadView(false);
        } else if (mCurrentOrientation == Configuration.ORIENTATION_LANDSCAPE) {
            uploadView(true);
        }
    }

    private void updatePlayListView() {
        if (mPlayListView != null) {
            mPlayListView.updateBroadcastList();
        }
    }

    boolean isNeedRefresh = false;

    @Override
    public void onPause() {
        super.onPause();
        //isNeedRefresh = true;

        if (broadcastCitySpinner.isWindowShow()) {
            isShowWindow = true;
            broadcastCitySpinner.collapse();
        }
    }

    private final IGeneralListener mGeneralListener = new IGeneralListener() {
        @Override
        public void getPlayListError(PlayItem playItem, int i, int i1) {
//            if (isPlayError()) {
//                onPlayChange();
//                manageOnPlayerListChanged();
//            }
        }

        @Override
        public void playUrlError(int code) {

        }
    };

    public boolean isReportFragment() {
        return true;
    }

    @Override
    protected void changeViewLayoutForStatusBar(View view) {

    }

    @Override
    protected void addFragmentRootViewPadding(View view) {

    }

    @Override
    protected boolean autoSetBackViewMarginLeft() {
        return false;
    }

    /**
     * 监听主题变化事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onThemeChangeEvent(UserCenterInter.ThemeChangeEvent event) {
        Log.d(TAG, "收到主题变化事件: " + event.getTheme());

        String theme = (event == null) ? "" : event.getTheme();
        if (TextUtils.isEmpty(theme)) {
            return;
        }

        // 处理isSameTheme事件：确认当前主题状态
        if ("isSameTheme".equals(theme)) {
            Log.d(TAG, "收到isSameTheme事件，延迟更新播放器信息");
            updatePlayerInfoDelayed();
            return;
        }

        // 根据皮肤框架的主题事件更新UI
        if ("night.skin".equals(theme) || "day.skin".equals(theme)) {
            Log.d(TAG, "主题切换为: " + theme + "，延迟更新播放器信息");
            updatePlayerInfoDelayed();
            showRadioCover();
        }
    }

    private void updatePlayerInfoDelayed() {
        new Handler(Looper.getMainLooper()).postDelayed(this::updatePlayerInfo, 300);
    }
}