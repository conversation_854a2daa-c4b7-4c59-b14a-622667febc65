package com.kaolafm.kradio.lib.utils;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.Rect;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.kaolafm.kradio.flavor.impl.DebugImpl;
import com.kaolafm.kradio.lib.base.AppDelegate;

import java.util.ArrayList;
import java.util.List;

/**
 * 控件热区调试工具
 * 用于可视化显示控件的点击区域，帮助调试多屏切换时的热区偏移问题
 * 
 * 使用方法：
 * 1. TouchAreaDebugger.enableDebug(true); // 启用调试
 * 2. TouchAreaDebugger.attachToActivity(activity); // 在Activity中启用
 * 3. TouchAreaDebugger.refreshDebugOverlay(activity); // 屏幕切换后刷新
 */
public class TouchAreaDebugger {
    private static final String TAG = "TouchAreaDebugger";

    // SharedPreferences相关
    private static final String PREF_NAME = "touch_area_debug";
    private static final String KEY_DEBUG_ENABLED = "debug_enabled";

    // 调试开关
    private static boolean sDebugEnabled = false;
    private static boolean sInitialized = false;

    // 存储已附加调试覆盖层的Activity
    private static final List<Activity> sAttachedActivities = new ArrayList<>();
    
    /**
     * 启用或禁用调试模式
     * @param enabled 是否启用调试
     */
    public static void enableDebug(boolean enabled) {
        // 允许在任何版本中启用调试功能
        sDebugEnabled = enabled;
        Log.i(TAG, "TouchArea debug " + (enabled ? "enabled" : "disabled") +
                   " (Build: " + (DebugImpl.isDebug() ? "Debug" : "Release") + ")");
    }

    /**
     * 检查调试模式是否启用
     */
    public static boolean isDebugEnabled() {
        return sDebugEnabled;
    }
    
    /**
     * 为Activity附加调试覆盖层
     * @param activity 目标Activity
     */
    public static void attachToActivity(Activity activity) {
        if (!isDebugEnabled() || activity == null) {
            return;
        }
        
        try {
            // 避免重复附加
            if (sAttachedActivities.contains(activity)) {
                Log.d(TAG, "Debug overlay already attached to " + activity.getClass().getSimpleName());
                return;
            }
            
            // 获取根视图
            ViewGroup rootView = activity.findViewById(android.R.id.content);
            if (rootView == null) {
                Log.e(TAG, "Cannot find content root view in " + activity.getClass().getSimpleName());
                return;
            }
            
            // 创建调试覆盖层
            DebugOverlayView debugOverlay = new DebugOverlayView(activity);
            debugOverlay.setLayoutParams(new FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.MATCH_PARENT
            ));
            
            // 添加到根视图
            rootView.addView(debugOverlay);
            sAttachedActivities.add(activity);
            
            Log.i(TAG, "Debug overlay attached to " + activity.getClass().getSimpleName());
            
            // 延迟刷新，确保布局完成
            debugOverlay.postDelayed(() -> refreshDebugOverlay(activity), 100);
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to attach debug overlay to " + activity.getClass().getSimpleName(), e);
        }
    }
    
    /**
     * 从Activity移除调试覆盖层
     * @param activity 目标Activity
     */
    public static void detachFromActivity(Activity activity) {
        if (activity == null) {
            return;
        }
        
        try {
            ViewGroup rootView = activity.findViewById(android.R.id.content);
            if (rootView != null) {
                // 查找并移除调试覆盖层
                for (int i = rootView.getChildCount() - 1; i >= 0; i--) {
                    View child = rootView.getChildAt(i);
                    if (child instanceof DebugOverlayView) {
                        rootView.removeView(child);
                        Log.i(TAG, "Debug overlay detached from " + activity.getClass().getSimpleName());
                    }
                }
            }
            
            sAttachedActivities.remove(activity);
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to detach debug overlay from " + activity.getClass().getSimpleName(), e);
        }
    }
    
    /**
     * 刷新调试覆盖层
     * 在屏幕配置变化后调用，重新扫描控件位置
     * @param activity 目标Activity
     */
    public static void refreshDebugOverlay(Activity activity) {
        if (!isDebugEnabled() || activity == null) {
            return;
        }
        
        try {
            ViewGroup rootView = activity.findViewById(android.R.id.content);
            if (rootView != null) {
                // 查找调试覆盖层并刷新
                for (int i = 0; i < rootView.getChildCount(); i++) {
                    View child = rootView.getChildAt(i);
                    if (child instanceof DebugOverlayView) {
                        ((DebugOverlayView) child).refreshClickableViews();
                        Log.d(TAG, "Debug overlay refreshed for " + activity.getClass().getSimpleName());
                        break;
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to refresh debug overlay for " + activity.getClass().getSimpleName(), e);
        }
    }
    
    /**
     * 收集所有可点击的View及其位置信息
     * @param rootView 根视图
     * @return 可点击View的信息列表
     */
    public static List<ViewInfo> collectClickableViews(View rootView) {
        List<ViewInfo> clickableViews = new ArrayList<>();
        
        if (rootView == null) {
            return clickableViews;
        }
        
        collectClickableViewsRecursive(rootView, clickableViews);
        
        Log.d(TAG, "Collected " + clickableViews.size() + " clickable views");
        return clickableViews;
    }
    
    /**
     * 递归收集可点击的View
     */
    private static void collectClickableViewsRecursive(View view, List<ViewInfo> result) {
        if (view == null || view.getVisibility() != View.VISIBLE) {
            return;
        }
        
        // 跳过调试覆盖层本身
        if (view instanceof DebugOverlayView) {
            return;
        }
        
        // 检查是否可点击
        if (view.isClickable() || view.hasOnClickListeners()) {
            Rect bounds = new Rect();
            view.getGlobalVisibleRect(bounds);
            
            ViewInfo info = new ViewInfo();
            info.view = view;
            info.bounds = bounds;
            info.className = view.getClass().getSimpleName();
            info.id = view.getId();
            
            result.add(info);
        }
        
        // 递归处理子View
        if (view instanceof ViewGroup) {
            ViewGroup group = (ViewGroup) view;
            for (int i = 0; i < group.getChildCount(); i++) {
                collectClickableViewsRecursive(group.getChildAt(i), result);
            }
        }
    }
    
    /**
     * View信息类
     */
    public static class ViewInfo {
        public View view;
        public Rect bounds;
        public String className;
        public int id;
        
        @Override
        public String toString() {
            String idName = "";
            try {
                if (id != View.NO_ID && view != null && view.getContext() != null) {
                    idName = view.getContext().getResources().getResourceEntryName(id);
                }
            } catch (Exception e) {
                // 忽略资源名称获取失败
            }
            
            return String.format("%s[%s] bounds=%s", 
                    className, 
                    idName.isEmpty() ? "no_id" : idName, 
                    bounds.toString());
        }
    }
    
    /**
     * 记录触摸事件信息
     * @param event 触摸事件
     * @param hitView 命中的View
     */
    public static void logTouchEvent(MotionEvent event, View hitView) {
        if (!isDebugEnabled()) {
            return;
        }
        
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            String hitInfo = "NONE";
            if (hitView != null) {
                String idName = "";
                try {
                    if (hitView.getId() != View.NO_ID) {
                        idName = hitView.getContext().getResources().getResourceEntryName(hitView.getId());
                    }
                } catch (Exception e) {
                    // 忽略
                }
                hitInfo = hitView.getClass().getSimpleName() + "[" + idName + "]";
            }
            
            Log.i(TAG, String.format("Touch at (%.1f, %.1f) hit: %s", 
                    event.getX(), event.getY(), hitInfo));
        }
    }
}
