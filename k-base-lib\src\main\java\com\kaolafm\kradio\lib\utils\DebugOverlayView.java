package com.kaolafm.kradio.lib.utils;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import java.util.List;

/**
 * 调试覆盖层View
 * 用于绘制控件的点击区域边界和触摸事件信息
 */
public class DebugOverlayView extends View {
    private static final String TAG = "DebugOverlayView";
    
    // 绘制相关
    private Paint mBorderPaint;
    private Paint mTextPaint;
    private Paint mTouchPaint;
    
    // 数据
    private List<TouchAreaDebugger.ViewInfo> mClickableViews;
    private float mLastTouchX = -1;
    private float mLastTouchY = -1;
    private long mLastTouchTime = 0;
    
    // 配置
    private static final int BORDER_COLOR = Color.RED;
    private static final int TOUCH_COLOR = Color.YELLOW;
    private static final int TEXT_COLOR = Color.WHITE;
    private static final int TEXT_BACKGROUND_COLOR = Color.argb(180, 0, 0, 0);
    private static final float BORDER_WIDTH = 3f;
    private static final float TOUCH_RADIUS = 20f;
    private static final long TOUCH_DISPLAY_DURATION = 2000; // 2秒
    
    public DebugOverlayView(Context context) {
        super(context);
        init();
    }
    
    public DebugOverlayView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    private void init() {
        // 设置为不可点击，让触摸事件穿透
        setClickable(false);
        setFocusable(false);
        
        // 初始化画笔
        mBorderPaint = new Paint();
        mBorderPaint.setColor(BORDER_COLOR);
        mBorderPaint.setStyle(Paint.Style.STROKE);
        mBorderPaint.setStrokeWidth(BORDER_WIDTH);
        mBorderPaint.setAntiAlias(true);
        
        mTextPaint = new Paint();
        mTextPaint.setColor(TEXT_COLOR);
        mTextPaint.setTextSize(24f);
        mTextPaint.setAntiAlias(true);
        
        mTouchPaint = new Paint();
        mTouchPaint.setColor(TOUCH_COLOR);
        mTouchPaint.setStyle(Paint.Style.FILL);
        mTouchPaint.setAntiAlias(true);
        
        Log.d(TAG, "DebugOverlayView initialized");
    }
    
    /**
     * 刷新可点击View列表
     */
    public void refreshClickableViews() {
        post(() -> {
            try {
                // 获取根视图
                ViewGroup rootView = getRootView().findViewById(android.R.id.content);
                if (rootView != null) {
                    mClickableViews = TouchAreaDebugger.collectClickableViews(rootView);
                    invalidate(); // 重新绘制
                    Log.d(TAG, "Refreshed clickable views: " + 
                            (mClickableViews != null ? mClickableViews.size() : 0));
                }
            } catch (Exception e) {
                Log.e(TAG, "Failed to refresh clickable views", e);
            }
        });
    }
    
    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        Log.d(TAG, "DebugOverlayView attached to window");
        
        // 延迟刷新，确保其他View已经布局完成
        postDelayed(this::refreshClickableViews, 200);
    }
    
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        Log.d(TAG, "DebugOverlayView detached from window");
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        if (!TouchAreaDebugger.isDebugEnabled()) {
            return;
        }
        
        // 绘制可点击View的边界
        drawClickableViewBorders(canvas);
        
        // 绘制触摸点
        drawTouchPoint(canvas);
        
        // 绘制调试信息
        drawDebugInfo(canvas);
    }
    
    /**
     * 绘制可点击View的边界
     */
    private void drawClickableViewBorders(Canvas canvas) {
        if (mClickableViews == null || mClickableViews.isEmpty()) {
            return;
        }
        
        for (TouchAreaDebugger.ViewInfo viewInfo : mClickableViews) {
            if (viewInfo.bounds != null && !viewInfo.bounds.isEmpty()) {
                // 转换为当前View的坐标系
                Rect localBounds = new Rect(viewInfo.bounds);
                
                // 绘制边界框
                canvas.drawRect(localBounds, mBorderPaint);
                
                // 绘制View信息文本（在边界框上方）
                String text = viewInfo.className;
                if (viewInfo.id != View.NO_ID) {
                    try {
                        String idName = getContext().getResources().getResourceEntryName(viewInfo.id);
                        text += "[" + idName + "]";
                    } catch (Exception e) {
                        // 忽略资源名称获取失败
                    }
                }
                
                float textX = localBounds.left;
                float textY = localBounds.top - 5;
                
                // 绘制文本背景
                Rect textBounds = new Rect();
                mTextPaint.getTextBounds(text, 0, text.length(), textBounds);
                canvas.drawRect(textX, textY - textBounds.height(), 
                               textX + textBounds.width(), textY, 
                               getPaintWithColor(TEXT_BACKGROUND_COLOR));
                
                // 绘制文本
                canvas.drawText(text, textX, textY, mTextPaint);
            }
        }
    }
    
    /**
     * 绘制触摸点
     */
    private void drawTouchPoint(Canvas canvas) {
        long currentTime = System.currentTimeMillis();
        
        // 只显示最近的触摸点
        if (mLastTouchX >= 0 && mLastTouchY >= 0 && 
            (currentTime - mLastTouchTime) < TOUCH_DISPLAY_DURATION) {
            
            canvas.drawCircle(mLastTouchX, mLastTouchY, TOUCH_RADIUS, mTouchPaint);
            
            // 绘制坐标文本
            String coordText = String.format("(%.0f, %.0f)", mLastTouchX, mLastTouchY);
            canvas.drawText(coordText, mLastTouchX + TOUCH_RADIUS + 10, 
                           mLastTouchY - TOUCH_RADIUS, mTextPaint);
        }
    }
    
    /**
     * 绘制调试信息
     */
    private void drawDebugInfo(Canvas canvas) {
        String info = "TouchArea Debug ON";
        if (mClickableViews != null) {
            info += " | Views: " + mClickableViews.size();
        }
        
        // 在右上角绘制调试状态
        float textX = getWidth() - 300;
        float textY = 50;
        
        // 绘制背景
        Rect textBounds = new Rect();
        mTextPaint.getTextBounds(info, 0, info.length(), textBounds);
        canvas.drawRect(textX - 10, textY - textBounds.height() - 5, 
                       textX + textBounds.width() + 10, textY + 5, 
                       getPaintWithColor(TEXT_BACKGROUND_COLOR));
        
        // 绘制文本
        canvas.drawText(info, textX, textY, mTextPaint);
    }
    
    /**
     * 获取指定颜色的Paint
     */
    private Paint getPaintWithColor(int color) {
        Paint paint = new Paint();
        paint.setColor(color);
        paint.setStyle(Paint.Style.FILL);
        return paint;
    }
    
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (!TouchAreaDebugger.isDebugEnabled()) {
            return false;
        }
        
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            // 记录触摸位置
            mLastTouchX = event.getX();
            mLastTouchY = event.getY();
            mLastTouchTime = System.currentTimeMillis();
            
            // 查找被触摸的View
            View hitView = findViewAt(mLastTouchX, mLastTouchY);
            
            // 记录触摸事件
            TouchAreaDebugger.logTouchEvent(event, hitView);
            
            // 重新绘制以显示触摸点
            invalidate();
        }
        
        // 不消费事件，让其穿透到下层View
        return false;
    }
    
    /**
     * 查找指定坐标处的View
     */
    private View findViewAt(float x, float y) {
        if (mClickableViews == null) {
            return null;
        }
        
        for (TouchAreaDebugger.ViewInfo viewInfo : mClickableViews) {
            if (viewInfo.bounds != null && viewInfo.bounds.contains((int)x, (int)y)) {
                return viewInfo.view;
            }
        }
        
        return null;
    }
    
    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        
        if (changed) {
            Log.d(TAG, "DebugOverlayView layout changed, refreshing clickable views");
            // 布局变化时刷新可点击View列表
            postDelayed(this::refreshClickableViews, 100);
        }
    }
}
