# 控件热区调试功能使用说明

## 功能概述
为了解决K-Radio应用在主副屏切换时控件点击热区偏移的问题，我们添加了一个可视化调试工具，可以直观地显示所有可点击控件的边界和触摸事件信息。

## 功能特性
1. **可视化边界显示**：红色边框显示所有可点击控件的实际边界
2. **控件信息标注**：显示控件类名和ID信息
3. **触摸点显示**：黄色圆圈显示最近的触摸位置
4. **触摸事件日志**：在Logcat中记录详细的触摸事件信息
5. **自动刷新**：屏幕配置变化时自动重新扫描控件位置
6. **调试开关**：只在Debug版本中生效，Release版本自动禁用

## 使用方法

### 1. 启用调试功能
调试功能已经集成到LauncherActivity中，会在应用启动时自动启用（仅Debug版本）。

### 2. 查看控件边界
- 启动应用后，所有可点击的控件周围会显示红色边框
- 控件上方会显示控件的类名和ID信息
- 右上角显示调试状态和扫描到的控件数量

### 3. 观察触摸事件
- 点击屏幕时，会在触摸位置显示黄色圆圈
- 圆圈旁边显示触摸坐标
- Logcat中会输出详细的触摸事件信息，包括命中的控件

### 4. 屏幕切换测试
1. 在副屏启动应用，观察控件边界
2. 切换到主屏，观察边界是否正确更新
3. 切换回副屏，检查边界是否与初始状态一致
4. 点击控件，验证热区是否与显示位置匹配

## 日志信息
在Logcat中搜索以下标签查看调试信息：
- `TouchAreaDebugger`：调试工具的状态和操作日志
- `DebugOverlayView`：覆盖层的绘制和刷新日志
- `kradio.home`：Fragment中的调试信息

### 典型日志示例
```
I/TouchAreaDebugger: TouchArea debug enabled
I/TouchAreaDebugger: Debug overlay attached to LauncherActivity
I/TouchAreaDebugger: Touch at (123.4, 567.8) hit: ImageView[home_search_btn]
D/DebugOverlayView: Refreshed clickable views: 15
```

## 问题诊断

### 如果看不到红色边框
1. 确认是Debug版本（Release版本不会显示）
2. 检查Logcat是否有TouchAreaDebugger的启用日志
3. 确认控件确实是可点击的（isClickable()或hasOnClickListeners()）

### 如果边界位置不正确
1. 观察屏幕切换前后边界的变化
2. 检查触摸事件日志，确认点击位置和命中控件
3. 对比不同屏幕下的控件坐标

### 如果热区偏移
1. 点击控件的显示位置，查看日志中实际命中的控件
2. 如果命中了错误的控件或没有命中，说明热区确实偏移了
3. 记录偏移的具体数值和方向

## 代码结构
- `TouchAreaDebugger.java`：主要的调试工具类
- `DebugOverlayView.java`：负责绘制调试信息的覆盖层
- `LauncherActivity.java`：集成了调试功能的初始化和刷新
- `HorizontalHomePlayerFragment.java`：在配置变化时刷新调试覆盖层

## 注意事项
1. 此功能仅在Debug版本中生效，不会影响Release版本的性能
2. 调试覆盖层不会拦截触摸事件，所有触摸都会正常传递给下层控件
3. 屏幕配置变化时会自动刷新，无需手动操作
4. 如果发现性能问题，可以通过`TouchAreaDebugger.enableDebug(false)`临时禁用

## 下一步建议
1. 使用此工具测试主副屏切换场景
2. 记录发现的热区偏移问题
3. 根据调试信息分析偏移的根本原因
4. 针对性地修复布局或触摸处理逻辑
