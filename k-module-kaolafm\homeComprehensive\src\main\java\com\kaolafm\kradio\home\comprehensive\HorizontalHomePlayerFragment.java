package com.kaolafm.kradio.home.comprehensive;

import static com.kaolafm.kradio.lib.utils.Constants.CLIENT_EXTRA_TYPE;
import static com.kaolafm.kradio.lib.utils.Constants.INVALID_NUM;
import static com.kaolafm.kradio.lib.utils.Constants.SEARCH_BY_KEYWORDS_EXTRA_TYPE;
import static com.kaolafm.kradio.lib.utils.Constants.START_PAGE;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.ad.api.model.Advert;
import com.kaolafm.ad.api.model.ImageAdvert;
import com.kaolafm.ad.comprehensive.KradioAdAudioManager;
import com.kaolafm.ad.comprehensive.ads.image.AdvertisingImagerImpl;
import com.kaolafm.ad.comprehensive.conflict.AdConflict;
import com.kaolafm.ad.expose.AdvertisingImager;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.categories.login.QRCodeFragment;
import com.kaolafm.kradio.category.FragmentFactory;
import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.common.SkinStateManager;
import com.kaolafm.kradio.common.event.LoginEvent;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.common.utils.TextLineUtil;
import com.kaolafm.kradio.common.widget.banner.KradioBannerView;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.ui.base.view.OvalImageView;
import com.kaolafm.kradio.config.ConfigSettingManager;
import com.kaolafm.kradio.constant.LiveComponentConst;
import com.kaolafm.kradio.constant.LoginProcessorConst;
import com.kaolafm.kradio.home.comprehensive.ad.KradioAdColumnManager;
import com.kaolafm.kradio.home.comprehensive.gallery.PageJumper;
import com.kaolafm.kradio.home.comprehensive.mvp.HomePlayerContract;
import com.kaolafm.kradio.home.comprehensive.mvp.HomePlayerPresenter;
import com.kaolafm.kradio.home.comprehensive.playerbar.NoScrollViewPager;
import com.kaolafm.kradio.home.comprehensive.widget.nav.HomeNavigationLayout;
import com.kaolafm.kradio.home.data.Category;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.HomePlayerNoNetMsgInter;
import com.kaolafm.kradio.lib.base.flavor.ICheckUpgraderInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioClickRetryInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioHomeExitInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioInitPlayerInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioLoginToastInter;
import com.kaolafm.kradio.lib.base.flavor.SyncInstrumentInter;
import com.kaolafm.kradio.lib.base.flavor.ThirdPlatformLoginer;
import com.kaolafm.kradio.lib.base.flavor.UserCenterInter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.base.ui.BaseShowHideFragment;
import com.kaolafm.kradio.lib.event.PagerJumpEvent;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.kradio.lib.utils.NetworkManager;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ScreenUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.TouchAreaDebugger;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.YTDataCache;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.lib.widget.tab.Tab;
import com.kaolafm.kradio.mine.MineUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.helper.intercept.HintInterceptManager;
import com.kaolafm.kradio.player.radiolive.LiveLifecycleListener;
import com.kaolafm.kradio.player.radiolive.LiveStateManager;
import com.kaolafm.kradio.player.radiolive.RadioLiveInfo;
import com.kaolafm.kradio.user.BackUserFragment;
import com.kaolafm.kradio.user.LoginManager;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.mian_tab.IMainTabView;
import com.kaolafm.mian_tab.MainTabPresenter;
import com.kaolafm.opensdk.OpenSDK;
import com.kaolafm.opensdk.api.config.ConfigSettingOption;
import com.kaolafm.opensdk.api.config.IConfigSettingOptionListener;
import com.kaolafm.opensdk.api.maintab.model.MainTabBean;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.model.item.LiveStreamPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import me.yokeyword.fragmentation.SupportFragment;
import me.yokeyword.fragmentation.anim.FragmentAnimator;

/**
 * HorizontalHomePlayerFragment 1.1.0
 */
@Route(path = RouterConstance.HOME_LIKE_COMPREHENSIVE_URL)
public class HorizontalHomePlayerFragment extends BaseShowHideFragment<HomePlayerPresenter>
        implements HomePlayerContract.View, IMainTabView, LoginManager.LoginListener {

    private static final String TAG = "kradio.home";

    ConstraintLayout homeRoom;
    HomeNavigationLayout mHnlHomeNavigation;
    NoScrollViewPager home_view_page;
    ViewStub mVsHomeNoNetwork;
    View mPcLoadingView;
    ImageView mIvHomeExitBtn;
    LinearLayout home_search_ll;
    LinearLayout home_login_ll;
    OvalImageView home_main_user_pic_iv;
    ImageView home_main_user_vip_iv;
    KradioBannerView<String> textBannerView;
    View userAvatarBg;

    View mHomeNoNetWorkRl;

    public static WeakReference<Context> mContext;

    private ConstraintSet mConstraintSet;

    private boolean isLaunch = true;

    private boolean isMediaPause;

    private static final String SAVED_MEDIA_PAUSE = "is_media_pause";


    private boolean needComeBack = false;

    /**
     * 是否在Fragment销毁时停止音频播放 true 为是，false 为否
     */
//    private boolean canStopAudio = true;

    private KRadioAudioPlayLogicInter mKRadioAudioPlayLogicInter;
    private KRadioHomeExitInter mKRadioHomeExitInter;

    private DynamicComponent dynamicComponent;

    /**
     * 是否已经初始化播放器
     */
    private boolean isInitPlayerContext = false;

    private boolean isErrorLayoutDisplay = false;

    public ArrayList<ComperhensiveHomeDateFragment> homeDateFragments = new ArrayList<>();
//    private int selectPage = 0;

    private Handler mHandler = new Handler();
    private Runnable radiusRunnable;
    private SkinStateManager.ILoadSkinListener mILoadSkinListener;

    private NetWorkListener mNetWorkListener;

    private static final long LOADING_MAX_TIME = 8 * 1000; // 加载中弹窗显示一定时间后自动消失
    private boolean mMainTabHasData = false;
    private final CountDownTimer mLoadingCountDownTimer = new CountDownTimer(LOADING_MAX_TIME, LOADING_MAX_TIME) {
        @Override
        public void onTick(long l) {
        }

        @Override
        public void onFinish() {
            Log.d(TAG, "timer finish " + mMainTabHasData);
            mLoadingCountDownTimer.cancel();
            // 如果当前加载view还显示，并且无数据，执行以下操作
            if (mPcLoadingView == null || mPcLoadingView.getVisibility() != View.VISIBLE) {
                return;
            }
            Log.d(TAG, "time's up, cancel loading view");
            hideLoading();
            if (mMainTabHasData) {
                return;
            }
            ViewUtil.setViewVisibility(mHomeNoNetWorkRl, View.VISIBLE);
            if (mHomeNoNetWorkRl != null && mHomeNoNetWorkRl.getVisibility() != View.VISIBLE) {
                showNoNetWorkView(ResUtil.getString(R.string.home_network_nosigin_or_retry));
            }
        }
    };

    public HorizontalHomePlayerFragment() {
    }

    @Override
    public void onEnterAnimationEnd(Bundle savedInstanceState) {
        super.onEnterAnimationEnd(savedInstanceState);
        KradioAdAudioManager.getInstance().mainActivityStart(getActivity());
        Intent intent = getActivity().getIntent();
        boolean isImageSKip = intent.getBooleanExtra("isImageSkip", false);
        boolean isSkip = intent.getBooleanExtra("isSkip", false);
        AdvertisingImagerImpl advertisingImager = (AdvertisingImagerImpl) AdvertisingManager.getInstance().getImager();
        if (advertisingImager != null) {
            advertisingImager.displayAttachedImage(getActivity(), isSkip, isImageSKip);
        }
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        mContext = new WeakReference<>(activity);
        //初始化图片加载库默认值
        ImageLoader.getInstance().initDefault(ResUtil.getDrawable(R.drawable.media_default_pic),
                ResUtil.getDrawable(R.drawable.media_default_pic));
        mKRadioAudioPlayLogicInter = ClazzImplUtil.getInter("KRadioAudioPlayLogicImpl");

    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        YTLogUtil.logStart(TAG, "onCreate", "首页");
        if (savedInstanceState != null) {
            isMediaPause = savedInstanceState.getBoolean(SAVED_MEDIA_PAUSE);
        }
        super.onCreate(savedInstanceState);
        noNetMsgInter = ClazzImplUtil.getInter("HomePlayerNoNetMsgImpl");

        LoginManager.getInstance().registerLoginListener(this);

        KradioAdAudioManager.getInstance().setContext(AppManager.getInstance().getMainActivity());

        AdConflict.isShowHome = true;
        initListener();

        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            mNetWorkListener = new NetWorkListener(this);
            NetworkManager.getInstance().addNetworkReadyListener(mNetWorkListener);
        } else {
            showLoading();
        }
        if (!OpenSDK.getInstance().isActivate()) {
            KradioSDKManager.getInstance().addUsableObserver(() -> {
                showLoading();
                mPresenter.getHotSearchWords();
                new MainTabPresenter(this).loadDate();
                isInitPlayerContext = false;
                initPlayerContext();
            });
            KradioSDKManager.getInstance().initAndActivate();
        }
    }

    @Override
    public void onLoading() {
        showLoading();
    }

    @Override
    public void onLoadFinish() {
        hideLoading();
    }

    /**
     * 首页导航条数据
     *
     * @param beans
     */
    @Override
    public void onTabDate(List<MainTabBean> beans) {
        //initView之后才能设置数据
        YTLogUtil.logStart(TAG, "onTabDate", "start");
//        hideLoading();
        int selectPage = YTDataCache.getSelectPage();
        mMainTabHasData = true;
        homeDateFragments.clear();
        List<Tab> tabList = new ArrayList<>();
        Tab tab;

        for (int i = 0; i < beans.size(); i++) {
            tab = new Tab();
            tab.code = beans.get(i).getPageId();
            tab.position = i;
            tab.title = beans.get(i).getName();
            tab.icon = beans.get(i).getIcon();

            if (beans.get(i).getIsLandingPage() == 1) {
                tab.select = true;
//                selectPage = i;
            } else {
                tab.select = false;
            }
            addDateFragment(beans.get(i));
            tabList.add(tab);
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_HOME_PAGE_NAVIGATION, tab.title, getPageId()
                    , ReportConstants.CONTROL_TYPE_SCREEN));
        }

        // 确保selectPage在有效范围内，避免越界
        if (selectPage >= tabList.size()) {
            Log.w(TAG, "onTabDate: selectPage " + selectPage + " is out of bounds, resetting to 0");
            selectPage = 0;
            YTDataCache.setSelectPage(selectPage);
        }

        mHnlHomeNavigation.setTabs(tabList);
        home_view_page.setOffscreenPageLimit(tabList.size());
        home_view_page.setAdapter(new FragmentPagerAdapter(getChildFragmentManager()) {
            @Override
            public Fragment getItem(int position) {
                return homeDateFragments.get(position);
            }

            @Override
            public int getCount() {
                return homeDateFragments.size();
            }
        });

        // 添加ViewPager页面变化监听器，确保状态同步
        home_view_page.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                // 不需要处理
            }

            @Override
            public void onPageSelected(int position) {
                // 当ViewPager页面改变时，同步更新导航栏和缓存状态
                Log.i(TAG, "ViewPager onPageSelected: " + position);
                YTDataCache.setSelectPage(position);
                // 同步更新导航栏的选中状态
                if (mHnlHomeNavigation != null) {
                    mHnlHomeNavigation.setCurrentTab(position);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                // 不需要处理
            }
        });

        // 设置正确的当前页面，确保UI状态和数据状态一致
        home_view_page.setCurrentItem(selectPage, false);
        // 同步更新导航栏的选中状态
        if (mHnlHomeNavigation != null) {
            mHnlHomeNavigation.setCurrentTab(selectPage);
        }

        Log.i(TAG, "onTabDate: set ViewPager currentItem to " + selectPage + ", tab title: " +
              (selectPage < tabList.size() ? tabList.get(selectPage).title : "unknown"));

        showOrHideLoadingView(false);
        YTLogUtil.logStart(TAG, "onTabDate", "end");
    }

    private String getZone(String pageId) {
        if (pageId == null) {
            return Category.HOME_DATE_ZONE_MAIN_PAGE;
        } else {
            String zone = "";
            switch (pageId) {
                case "201000"://推荐
                    zone = Category.HOME_DATE_ZONE_MAIN_PAGE;
                    break;
                case "160000"://新闻
                    zone = Category.HOME_DATE_ZONE_NEWS_PAGE;
                    break;
                case "160001"://电台
                    zone = Category.HOME_DATE_ZONE_RADIO_PAGE;
                    break;
                case "160004"://有声书
                    zone = Category.HOME_DATE_ZONE_ALBUM_PAGE;
                    break;
                case "160002"://听电视
                    zone = Category.HOME_DATE_ZONE_TV_PAGE;
                    break;
                case "160003"://福利活动
                    zone = Category.HOME_DATE_ZONE_ACTIVITY_PAGE;
                    break;
                default:
                    zone = Category.HOME_DATE_ZONE_MAIN_PAGE;
            }

            return zone;
        }
    }

    private void addDateFragment(String pageId) {
        String zone = getZone(pageId);
        ComperhensiveHomeDateFragment comperhensiveHomeDateFragment = ComperhensiveHomeDateFragment.newInstance(pageId, zone);
        comperhensiveHomeDateFragment.loadDate(zone);
        homeDateFragments.add(comperhensiveHomeDateFragment);
    }

    private void addDateFragment(MainTabBean bean) {

        String pageId = bean.getPageId();
        String zone = bean.getLinkZone();
        if (pageId == null) {
            pageId = "";
        }
        if (zone == null) {
            zone = "";
        }

        ComperhensiveHomeDateFragment comperhensiveHomeDateFragment = ComperhensiveHomeDateFragment.newInstance(pageId, zone);
        comperhensiveHomeDateFragment.loadDate(zone);
        homeDateFragments.add(comperhensiveHomeDateFragment);
    }

    @Override
    public void onTabDateError(String e) {
        Logger.e(TAG, e);
        mMainTabHasData = false;
        hideLoading();
        if (e.equals(ResUtil.getString(R.string.home_network_nosigin_or_retry))) {
            ToastUtil.showInfo(getContext(), ResUtil.getString(R.string.network_error_toast));
        }
        showNoNetWorkView(e);
    }

    @Override
    public void onLoginStateChange(int cp, boolean isLogin) {
        updateUserLoginState();
    }

    @Override
    public void onCancel() {

    }

    private void updateUserLoginState() {
        if (UserInfoManager.getInstance().isUserLogin()) {
            home_main_user_pic_iv.setVisibility(View.VISIBLE);
//            userAvatarBg.setVisibility(View.VISIBLE);
            ImageLoader.getInstance().displayImage(getContext(), UserInfoManager.getInstance().getUserFavicon(), home_main_user_pic_iv);

            if (UserInfoManager.getInstance().getVip() == 1) {
                home_main_user_vip_iv.setVisibility(View.VISIBLE);
            } else {
                home_main_user_vip_iv.setVisibility(View.INVISIBLE);
            }
        } else {
            home_main_user_pic_iv.setImageDrawable(ResUtil.getDrawable(R.drawable.user_no_login_icon));
            home_main_user_vip_iv.setVisibility(View.INVISIBLE);
//            userAvatarBg.setVisibility(View.GONE);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        Log.i(TAG, "HorizontalHomePlayerFragment    onStart");
        dealPageJump();
    }

    /**
     * 设置播放状态
     */
    public void setSelected() {
        int selectPage = YTDataCache.getSelectPage();
        if (homeDateFragments.size() > selectPage)
            homeDateFragments.get(selectPage).setSelected();
    }

    /**
     * 设置播放异常
     */
    public void setPlayEnd() {
        int selectPage = YTDataCache.getSelectPage();
        if (homeDateFragments.size() > selectPage)
            homeDateFragments.get(selectPage).setPlayEnd();
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        PageJumper.getInstance().init(getActivity(), this);
    }

    /**
     * 首页是否首次走onResume true为是，false为否
     */
    private boolean isFirstOnResume = true;

    @Override
    public void onResume() {
        super.onResume();
        //数据上报
        reportEvent();
        Log.i(TAG, "HorizontalHomePlayerFragment    onResume");
        //如果文字轮播中切换了页面，回来后要继续轮播
        if (textBannerView != null && isTextBannerNeedContinuePlay) {
            textBannerView.startViewAnimator();
        }
        updateUserLoginState();
        dealPageJump();
        if (isFirstOnResume) {
            isFirstOnResume = false;
            KRadioLoginToastInter kRadioLoginToastInter = (KRadioLoginToastInter) ClazzImplUtil.getInter("KRadioLoginToastImpl");
            if (kRadioLoginToastInter != null) {
                kRadioLoginToastInter.showLoginToast(this);
            }
        }
    }

    /**
     * 数据上报
     */
    private void reportEvent() {
        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(
                ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_MINE, null, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN
        ));
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_MAIN;
    }

    @Override
    public void onDestroyView() {
        Log.i(TAG, "HomePlayerFragment onDestroyView");
        LoginManager.getInstance().unregisterLoginListener(this);
        mHnlHomeNavigation.destroy();
        SyncInstrumentInter mSyncInstrumentInter = ClazzImplUtil.getInterSingleInstance("SyncInstrumentImpl");
        Log.e(TAG, "onDestroyView: mSyncInstrumentInter = " + mSyncInstrumentInter);
        if (mSyncInstrumentInter != null) {
            mSyncInstrumentInter.releaseSyncInstrument();
        }
        super.onDestroyView();
    }

    private void initSkinListener() {
        mILoadSkinListener = () -> {
            if (radiusRunnable != null) {
                mHandler.removeCallbacks(radiusRunnable);
            }
            radiusRunnable = this::resetViewRadius;
            mHandler.postDelayed(radiusRunnable, 1000L);
        };
        SkinStateManager.getInstance().addLoadSkinListener(mILoadSkinListener);
    }

    @Deprecated
    private void resetViewRadius() {
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.i(TAG, "HomePlayerFragment onDestroy");
        SkinStateManager.getInstance().removeLoadSkinListener(mILoadSkinListener);
        mContext.clear();
        TextLineUtil.clearTextLineCache();

        ToastUtil.release();
        KradioAdColumnManager.getInstance().unBindHomeRecyclerViewHelper();

        Log.i(TAG, "KradioAdAudioManager.getInstance().setContext(null);");
        KradioAdAudioManager.getInstance().setContext(null);
        if (mLoadingCountDownTimer != null) {
            mLoadingCountDownTimer.cancel();
        }
        NetworkManager.getInstance().removeNetworkReadyListener(mNetWorkListener);
    }

    @Override
    public boolean useEventBus() {
        return true;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_home;
    }

    @Override
    protected int getLayoutId_Tow() {
        return R.layout.fragment_home_tow;
    }

    HomePlayerNoNetMsgInter noNetMsgInter;

    @Override
    public void initView(View view) {
        initSkinListener();
        homeRoom = view.findViewById(R.id.home_room);
        mHnlHomeNavigation = view.findViewById(R.id.hnl_home_navigation);
        home_view_page = view.findViewById(R.id.home_view_page);
        mVsHomeNoNetwork = view.findViewById(R.id.vs_home_no_network);
        mPcLoadingView = view.findViewById(R.id.pc_loading);
        mIvHomeExitBtn = view.findViewById(R.id.iv_home_exit_btn);
        home_search_ll = view.findViewById(R.id.home_search_ll);
        home_login_ll = view.findViewById(R.id.home_login_ll);
        home_main_user_pic_iv = view.findViewById(R.id.home_main_user_pic_iv);
        home_main_user_vip_iv = view.findViewById(R.id.home_main_user_vip_iv);
        textBannerView = view.findViewById(R.id.textBannerView);
        userAvatarBg = view.findViewById(R.id.user_avatar_bg);

        initTextBannerView();
        if (mHomeNoNetWorkRl != null) {
            mHomeNoNetWorkRl.setVisibility(View.GONE);
        }


        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            Log.i(TAG, "initView---->no netWork");
            if (noNetMsgInter != null) {
                showNoNetWorkView(noNetMsgInter.getNoNetMsg());
            } else {
                ToastUtil.showInfo(getContext(), ResUtil.getString(R.string.network_error_toast));
                showNoNetWorkView(ResUtil.getString(R.string.home_network_nosigin_or_retry));
            }

        }
        //搜索点击
        home_search_ll.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AdvertisingImagerImpl imager = (AdvertisingImagerImpl) AdvertisingManager.getInstance().getImager();
                // fix 空指针crash
                if (imager != null) {
                    Advert advert = imager.getAdvert();
                    if (advert instanceof ImageAdvert) {
                        imager.skip((ImageAdvert) advert);
                    }
                } else {
                    Log.w(TAG, "AdvertisingImagerImpl is null!!!!");
                }

                String currentData = textBannerView.getCurrentData();
                if (ResUtil.getString(R.string.comprehensive_home_search_hint).equals(currentData)) {
                    currentData = null;
                }
                Map<String, String> map = new HashMap<>();
                map.put(Constants.ROUTER_PARAMS_KEY_EXTRA, currentData);
                RouterManager.getInstance().navigateToPage(getContext(), Constants.PAGE_ID_SEARCH, map);
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(
                        ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH, currentData == null ? "首页搜索" : currentData, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN
                ));
            }
        });
        home_login_ll.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RouterManager.getInstance().consumeRoute(Constants.PAGE_ID_ACCOUNT_MAIN, null);
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(
                        ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_MINE, null, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN
                ));
            }
        });
        mKRadioHomeExitInter = ClazzImplUtil.getInter("KRadioHomeExitImpl");
        if (mKRadioHomeExitInter != null) {
            mKRadioHomeExitInter.isShowHomeExitBtn(mIvHomeExitBtn, true, getActivity());
        }
        mHnlHomeNavigation.setOnMoreBtnClickListener(v -> jumpToAllCategoriesFragment(0, 0, -1L));
        mHnlHomeNavigation.setHomeNavigationChangeListener(new HomeNavigationLayout.IHomeNavigationChangeListener() {
            @Override
            public void onHomeNavigationChange(int position) {
                //导航条选中监听
                if (homeDateFragments.size() > position) {
                    home_view_page.setCurrentItem(position);
//                    selectPage = position;
                    YTDataCache.setSelectPage(position);
                    setSelected();
                    Tab currentTab = mHnlHomeNavigation.getTabAt(position);
                    if (currentTab != null) {
                        Log.i(TAG, "onHomeNavigationChange: switched to tab " + position + " (" + currentTab.title + ")");
                        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_HOME_PAGE_NAVIGATION, currentTab.title, getPageId()
                                , ReportConstants.CONTROL_TYPE_SCREEN));
                    }
                }
            }
        });
        mHnlHomeNavigation.setPageId(getPageId());
        new MainTabPresenter(this).loadDate();
        resetViewRadius();
    }

    private void initTextBannerView() {
        textBannerView.setAdapter(new KradioBannerView.KradioBannerAdapter<String>() {
            @Override
            public View onCreateView(ViewGroup container) {
                TextView textView = new TextView(container.getContext());
                //任意设置你的文字样式，在这里
                textView.setSingleLine(true);
                textView.setEllipsize(TextUtils.TruncateAt.END);
                textView.setTextColor(ResUtil.getColor(R.color.search_text_hint_color));
                textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResUtil.getDimension(R.dimen.m24));
                textView.setGravity(Gravity.LEFT | Gravity.CENTER_VERTICAL);
                textView.getPaint().setFlags(0 | Paint.ANTI_ALIAS_FLAG);//字体划线
                textView.setTypeface(null, Typeface.NORMAL);//字体样式
                return textView;
            }

            @Override
            public void onBindView(@NonNull View itemView, String s, int position) {
                ((TextView) itemView).setText(s);
            }
        });
    }

    @Override
    protected HomePlayerPresenter createPresenter() {
        return new HomePlayerPresenter(getContext(), this);
    }

    @Override
    public void showLoading() {
        showOrHideLoadingView(true);

        if (mLoadingCountDownTimer != null) {
            mLoadingCountDownTimer.cancel();
        }
        mLoadingCountDownTimer.start();
    }

    @Override
    public void hideLoading() {
        showOrHideLoadingView(false);
    }

    @Override
    public void showError(String error) {
        Log.i(TAG, "showError----->error = " + error);
        hideLoading();
        showNoNetWorkView(error);
    }

    public void showNoNetWorkView(String error) {
        isErrorLayoutDisplay = true;
        showOrHideLoadingView(false);
        if (mVsHomeNoNetwork != null) {
            if (null == mHomeNoNetWorkRl) {
                mHomeNoNetWorkRl = mVsHomeNoNetwork.inflate();
            }
            if (StringUtil.isNotEmpty(error)) {
                TextView tvNetworkNosign = mHomeNoNetWorkRl.findViewById(R.id.tv_network_nosign);
                tvNetworkNosign.setText(error);
            }
            // 支持点击重试
            KRadioClickRetryInter mKRadioClickRetryInter = ClazzImplUtil.getInter(
                    "KRadioClickRetryInterImpl");
            if (mKRadioClickRetryInter == null || mKRadioClickRetryInter.canRetry()) {
                ImageView ivNetworkNoSign = mHomeNoNetWorkRl.findViewById(R.id.network_nosigin);
                ivNetworkNoSign.setOnClickListener(v -> {
                    retryLoad();
                    TextView tvNetworkNosign = mHomeNoNetWorkRl.findViewById(R.id.tv_network_nosign);
                    String text = null;
                    if (tvNetworkNosign != null) {
                        text = tvNetworkNosign.getText().toString();
                    }
                    ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
                });
            }
        }
        ViewUtil.setViewVisibility(mHomeNoNetWorkRl, View.VISIBLE);
        if (mHomeNoNetWorkRl != null) {
            TextView tvNetworkNosign = mHomeNoNetWorkRl.findViewById(R.id.tv_network_nosign);
            String text = null;
            if (tvNetworkNosign != null) {
                text = tvNetworkNosign.getText().toString();
            }
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
        }
    }

    private void retryLoad() {
        hideErrorLayout();
        if (!OpenSDK.getInstance().isActivate()) {
            KradioSDKManager.getInstance().addUsableObserver(() -> {
                showLoading();
                mPresenter.getHotSearchWords();
                new MainTabPresenter(this).loadDate();
                isInitPlayerContext = false;
                initPlayerContext();
            });
            KradioSDKManager.getInstance().initAndActivate();
        } else {
            showLoading();
            mPresenter.getHotSearchWords();
            new MainTabPresenter(this).loadDate();
            initPlayerContext();
        }
    }

    public void hideErrorLayout() {
        isErrorLayoutDisplay = false;
        if (mHomeNoNetWorkRl != null) {
            ViewUtil.setViewVisibility(mHomeNoNetWorkRl, View.GONE);
        }
    }

    @Override
    public void showHotSearchWordsView(List<String> hotWords) {
        ConfigSettingManager.getInstance().getConfigSetting(new IConfigSettingOptionListener() {
            @Override
            public void onGetSuccess(ConfigSettingOption configSettingOption) {
                if (configSettingOption.getSearchHotWordIntegererval() == null) {
                    return;
                }
                long searchHotWordInterval = configSettingOption.getSearchHotWordIntegererval();
                if (searchHotWordInterval == 0) searchHotWordInterval = 5000L;
                textBannerView.setInterval(searchHotWordInterval);
                if (ListUtil.isEmpty(hotWords)) {
                    textBannerView.setDatas(new ArrayList<String>() {{
                        add(ResUtil.getString(R.string.comprehensive_home_search_hint));
                    }});
                } else {
                    textBannerView.setDatas(hotWords);
                }
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(
                        ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_SEARCH, "首页搜索", getPageId(), ReportConstants.CONTROL_TYPE_SCREEN
                ));
            }

            @Override
            public void onGetFailure(ApiException e) {

            }
        });
    }


    @Override
    public void onSaveInstanceState(Bundle outState) {
        isMediaPause = !PlayerManagerHelper.getInstance().isPlaying();
        outState.putBoolean(SAVED_MEDIA_PAUSE, isMediaPause);
        super.onSaveInstanceState(outState);
    }

    /**
     * attachPlayer和detachPlayer应该配对调用，所以放到onViewCreated和onDestroyView中
     * 既然播放一旦启动，就需要有界面体现播放器的状态，例如，要有loading体现onPrepare,所以，把启动播放
     * 放到View创建完成比较合理。
     */
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Log.i(TAG, "HomePlayerFragment onViewCreated");
        if (isLaunch) {
            isLaunch = false;
            checkPresenterIsNull();
            mPresenter.initData();
        }
        // 第一次初始化 需要获取到categories的数据，如果为空，就不会categories的第一条 蔚来电台
        // 不是第一次的情况下也会出现这个问题，playFirstCategory() 要求要有categories 数据
        // 放在后台一会儿，重新打开也会走这套逻辑，需要初始化categories 数据
        // fix  #33561
        Log.i("initplayer", "initplayer");
        //  尽量的将 attachPlayer 和 initplayer 按照先后执行顺序进行处理，可以大规模的降低 播放器初始化失败的情况。
        // 需要考虑到 拉取历史的，网络获取categray的第一条，播放器会根据一定逻辑进行播放。
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001223223038?userId=1881599问题
//        attachPlayer();

        ICheckUpgraderInter mICheckUpgraderInter = ClazzImplUtil.getInter("CheckUpgraderImpl");
        if (mICheckUpgraderInter != null) {
            mICheckUpgraderInter.checkUpgrade(false);
        }
        textBannerView.setDatas(new ArrayList() {{
            add(ResUtil.getString(R.string.comprehensive_home_search_hint));
        }});
        mPresenter.getHotSearchWords();
    }

    public void onPlayerInitComplete(boolean isSuccess) {
        Log.i(TAG, "onPlayerInitComplete-------->isSuccess = " + isSuccess);
        if (isSuccess) {
            checkPresenterIsNull();
            mPresenter.onPlayerInitSuccess();
            if (mPresenter != null) {
                mPresenter.onPlayerInitSuccess();
                KRadioInitPlayerInter inter = ClazzImplUtil.getInter("KRadioInitPlayerImpl");
                if (inter != null && inter.isInit() && KradioAdAudioManager.getInstance().getAdvertPlayerimpl() == null) {
                    initPlayerContext();
                }
            }
        }
    }

    public void initPlayerContext() {
        Log.i(TAG, "initPlayerContext......");
        if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), false)) {
            return;
        }
        if (isInitPlayerContext) {
            return;
        }
        isInitPlayerContext = true;
        Activity activity = getActivity();
        boolean hasPlayExtra = false;
        if (activity != null) {
            Intent intent = activity.getIntent();
            int extraType = INVALID_NUM;
            try {
                extraType = intent.getIntExtra(CLIENT_EXTRA_TYPE, INVALID_NUM);
            } catch (Exception e) {
            }
            Log.i(TAG, "demoInitPBP--------->extraType = " + extraType);
            if (extraType == SEARCH_BY_KEYWORDS_EXTRA_TYPE) {
                hasPlayExtra = true;
            }
        }
        //此处逻辑跟120版本差距较大，后期需要合并120新增逻辑
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (hasPlayExtra || (playItem != null && playItem.getType() != PlayerConstants.RESOURCES_TYPE_INVALID)) {
            // 播放器 还在播放,启动考拉
            Log.i(TAG, "demoInitPBP--------->" + playItem);
            ThirdPlatformLoginer thirdPlatformLoginer = ClazzImplUtil.getInter("ThirdPlatformLoginerImpl");
            if (thirdPlatformLoginer != null) {
                //playHistory();
                thirdPlatformLoginer.login(AppDelegate.getInstance().getContext().getApplicationContext(),
                        new ThirdPlatformLoginer.Callback() {
                            @Override
                            public void onSuccess() {
                            }

                            @Override
                            public void onFailure() {
                            }
                        });
            }
            if (mKRadioAudioPlayLogicInter != null) {
                mKRadioAudioPlayLogicInter.doStartInPlay();
            }
        } else {
            // 正常的app 退出逻辑
            if (mKRadioAudioPlayLogicInter != null) {
                //播放指定渠道中的播放逻辑
                boolean status = mKRadioAudioPlayLogicInter.autoPlayAudio(AppDelegate.getInstance().getContext());
                if (!status) {
                    checkPresenterIsNull();
                    mPresenter.playNetOrLocal();
                } else {
                    ThirdPlatformLoginer thirdPlatformLoginer = ClazzImplUtil
                            .getInter("ThirdPlatformLoginerImpl");
                    if (thirdPlatformLoginer != null) {
                        //playHistory();
                        thirdPlatformLoginer.login(AppDelegate.getInstance().getContext().getApplicationContext(),
                                new ThirdPlatformLoginer.Callback() {
                                    @Override
                                    public void onSuccess() {
                                    }

                                    @Override
                                    public void onFailure() {
                                    }
                                });
                    }
                }
            } else {
                checkPresenterIsNull();
                mPresenter.playNetOrLocal();
            }

            ICheckUpgraderInter mICheckUpgraderInter = ClazzImplUtil.getInter("CheckUpgraderImpl");
            if (mICheckUpgraderInter != null) {
                mICheckUpgraderInter.checkUpgrade(false);
            }
        }
    }

    /**
     * 跳转到全部分类
     */
    private void jumpToAllCategoriesFragment(int type, long id, long secondId) {
        Map<String, String> map = new HashMap<>();
        map.put(Constants.ROUTER_PARAMS_KEY_EXTRA, id + "");

        RouterManager.getInstance().consumeRoute(Constants.PAGE_ID_CATEGORY, id);
//        RouterManager.getInstance().navigateToPage(getContext(), Constants.PAGE_ID_CATEGORY, map);
    }

    @Subscribe
    public void event(JumpToFragentEvent event) {
        jumpToAllCategoriesFragment(event.getType(), event.getId(), event.getSecondId());
    }

    /**
     * 跳到登录页面
     */
    public void jumpToLoginPage(int cpType) {
        ToastUtil.showNormal(getContext(), R.string.please_login_qqmusic);
        //如果已经二维码页面就不在跳转
        if (getTopFragment() instanceof QRCodeFragment) {
            return;
        }
        Fragment fragment = FragmentFactory.comeHereLogin();
        HorizontalHomePlayerFragment.this.extraTransaction().start((SupportFragment) fragment);
    }

    /**
     * 跳到登录页面
     */
    public void jumpToCoinPage() {
        Fragment fragment = MineUtil.INSTANCE.showCoinFragment();
        HorizontalHomePlayerFragment.this.extraTransaction().start((SupportFragment) fragment);
    }

    /**
     * 跳到kradio登录或注册页面
     */
    public void jumpToKRadioLoginPage() {
        Log.i(TAG, "jumpToKRadioLoginPage");
        //todo 需要重新请求接口刷新页面（参考播放历史记录）
        if (getTopFragment() instanceof BackUserFragment) {
            // 解决https://app.huoban.com/tables/2100000007530121/items/2300001878074573?userId=1229522问题
            BackUserFragment backUserFragment = (BackUserFragment) getTopFragment();
            //fixed 由于添加了"已购"标题，"账号登陆的索引需要变成3"
            backUserFragment.updateCurrentTabPosition(3);
            Log.i(TAG, "getTopFragment() instanceof BackUserFragment");
            return;
        }
        jumpToNewLoginPage();
    }

    private void jumpToNewLoginPage() {
        //fixed 由于添加了"已购"标题，"账号登陆的索引需要变成3"
        HintInterceptManager.getInstance();
        BackUserFragment backUserFragment = BackUserFragment.newInstance(3);
        if (needComeBack) {
            needComeBack = false;
            if (PlayerManagerHelper.getInstance().isActionFromUser()) {
                backUserFragment.setBackData(LoginProcessorConst.BACKTYPE_POP, Constants.PLAYER_ITEM_CLICK);
            } else {
                backUserFragment.setBackData(LoginProcessorConst.BACKTYPE_POP, Constants.PLAYER_ITEM_CAROUSEL);
            }
        }
        HorizontalHomePlayerFragment.this.extraTransaction().start(backUserFragment);
    }

    /**
     * fragment back
     */
    public void back() {
        HorizontalHomePlayerFragment.this.pop();
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPagerJumpEvent(PagerJumpEvent event) {
        int page = event.page;
        switch (page) {
            case PagerJumpEvent.PAGE_CATEGORY: {
                Bundle bundle = event.bundle;
                int type = bundle.getInt("category_type");
                int firstId = bundle.getInt("category_id");
                int secondId = bundle.getInt("subcategory_id");
                jumpToAllCategoriesFragment(type, firstId, secondId);
            }
            break;
            default:
                //do-nothing
                break;
        }
    }

    HomePlayerSecondPlayerStateListenerWrapper playerSecondPlayerStateListenerWrapper;

    String getRadioId() {
        String radioId = Constants.BLANK_STR;
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItem != null) {
            radioId = playItem.getRadioId();
        }
        return radioId;
    }

    void setIsMediaPause(boolean flag) {
        isMediaPause = flag;
        setSelected();
    }

    @Override
    public FragmentAnimator onCreateFragmentAnimator() {
        //首页加入动画，解决跳转二级界面出现花屏或者空白的现象，原因是因为二级界面做动画的时候home被hide了。分机型展示的效果不一样。
        return new FragmentAnimator(R.anim.enter_default_transition_animation,
                R.anim.exit_default_transition_animation,
                R.anim.from_exit_default_transition_animation,
                R.anim.from_enter_default_transition_animation);
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
//            applyAnim();
            //   mPlayerBar.updateInfo(CP.KRadio, PlayerManager.getInstance().getCurPlayItem());
        } else {
            //首页隐藏时,停止动画
            Log.i(TAG, "hidden: " + hidden);
//            CoinAnimHelper.getInstance().stop(mHfvHomeFunctions);
            AdvertisingImager advertisingImager = AdvertisingManager.getInstance().getImager();
            if (advertisingImager != null) {
                advertisingImager.skip(null);
            }
        }

    }


    /**
     * 此函数针对BaseFragment做了空实现，不能去掉
     */
    @Override
    protected void changeViewLayoutForStatusBar(View view) {
    }

    /**
     * 根据横竖屏显示。
     */
    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
        showScreenInfo();
    }

    private void showScreenInfo() {
        int screenWidth = ScreenUtil.getScreenWidth();
        int screenHeight = ScreenUtil.getScreenHeight();
        int x100 = getResources().getDimensionPixelSize(R.dimen.x100);
        int y100 = getResources().getDimensionPixelSize(R.dimen.y100);
        Log.i(TAG, "showScreenInfo .... screeninfo: " + screenWidth + " *" + screenHeight);
        Log.i(TAG, "showScreenInfo .... dimen100: " + x100 + " *" + y100);
    }


    private LiveLifecycleListener mLiveLifecycleListener = new LiveLifecycleListener() {
        @Override
        public void onState(int state, RadioLiveInfo radioLiveInfo) {
            if (state == LiveStreamPlayItem.Living) {
                Bundle extend = getExtend();
                String action = extend.getString("operating");
                if (TextUtils.equals(action, "yes")) {
                    BaseFragment topFragment = (BaseFragment) getTopFragment();
                    ComponentClient.obtainBuilder(LiveComponentConst.NAME)
                            .setActionName(LiveComponentConst.START_FRAGMENT)
                            .addParam("liveInfo", radioLiveInfo.getLiveDetails())
                            .addParam("context", topFragment)
                            .addParam("containerId", R.id.launcher_main_layout)
                            .build().callAsync();
                }
            }
        }
    };

    public void registerLiveLifecycleListener() {
        LiveStateManager.getInstance()
                .registerLiveLifecycleListener(Long.parseLong(getRadioId()), mLiveLifecycleListener);
    }

    private void dealPageJump() {
        FragmentActivity fragmentActivity = getActivity();
        if (fragmentActivity != null && fragmentActivity.getIntent() != null) {
            Intent intent = fragmentActivity.getIntent();
            int page = -1;
            try {
                page = intent.getIntExtra(START_PAGE, -1);
            } catch (Exception e) {
            }
            Log.i(TAG, "dealPageJump-------->page = " + page);
            switch (page) {
                case Constants.START_PAGE_LOGIN:
                    jumpToKRadioLoginPage();
                    intent.putExtra(START_PAGE, -1);
                    break;
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void startToLoginPage(LoginEvent event) {
        Log.i(TAG, "startToLoginPage---------->event = " + event);
        if (event.getStartPage() == Constants.START_PAGE_LOGIN) {
            jumpToKRadioLoginPage();
        }
    }


    private void initListener() {
        Log.i(TAG, "HomePlayerFragment initListener");
        NetworkManager networkManager = NetworkManager.getInstance();
        if (!networkManager.isNotHasNetwork()) {
            Log.i(TAG, "networkManager hasNetwork");
            return;
        }
        if (networkManager.getNetworkState() == NetworkManager.NETWORK_NO) {
            Log.i(TAG, "当前没有网络");
            if (noNetMsgInter != null) {
                showNoNetWorkView(noNetMsgInter.getNoNetMsg());
            } else {
                ToastUtil.showInfo(getContext(), ResUtil.getString(R.string.network_error_toast));
                showNoNetWorkView(ResUtil.getString(R.string.home_network_nosigin_or_retry));
            }
        }
        Log.i(TAG, "trouble shoot addNetworkReadyListener");
        networkManager.addNetworkReadyListener(
                new NetworkManager.INetworkReady() {
                    @Override
                    public void networkChange(boolean hasNetwork) {
                        Log.i(TAG, "网络变化: " + hasNetwork);
                        if (hasNetwork) {
                            retryRefreshHomeData();
                            NetworkManager.getInstance().removeNetworkReadyListener(this);
                        } else {
                            if (mVsHomeNoNetwork != null) {
                                if (noNetMsgInter != null) {
                                    showNoNetWorkView(noNetMsgInter.getNoNetMsg());
                                } else {
                                    ToastUtil.showInfo(getContext(), ResUtil.getString(R.string.network_error_toast));
                                    showNoNetWorkView(ResUtil.getString(R.string.home_network_nosigin_or_retry));
                                }
                            }
                        }
                    }
                });

    }

    private void retryRefreshHomeData() {
        if (!OpenSDK.getInstance().isActivate()) {
            Log.i(TAG, "网络变化: 没有激活....");
            KradioSDKManager.getInstance().addUsableObserver(() -> {
                checkPresenterIsNull();
                mPresenter.initData();
                initPlayerContext();
            });
            KradioSDKManager.getInstance().initAndActivate();
        } else {
            Log.i(TAG, "网络变化: 已经激活");
            checkPresenterIsNull();
            mPresenter.initData();
            initPlayerContext();
        }
    }

    /**
     * 主要为了解决Monkey测试出现的空指针问题
     */
    private void checkPresenterIsNull() {
        if (mPresenter == null) {
            mPresenter = createPresenter();
        }
    }

    private void showOrHideLoadingView(boolean isShow) {
        Log.i(TAG, "showOrHideLoadingView: " + isShow);
        if (mPcLoadingView == null && rootView != null) {
            mPcLoadingView = rootView.findViewById(R.id.pc_loading);
        }
        ViewUtil.setViewVisibility(mPcLoadingView, isShow ? View.VISIBLE : View.GONE);
        if (isShow) {
            ViewUtil.setViewVisibility(mHomeNoNetWorkRl, View.GONE);
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (!isErrorLayoutDisplay) {
            ViewUtil.setViewVisibility(mHomeNoNetWorkRl, View.GONE);
        }

        // 修复：强制刷新UI以适配新的屏幕配置
        // 解决副主副屏切换时分辨率异常的问题
        forceRefreshUIForScreenChange(newConfig);
    }

    /**
     * 强制刷新UI以适配新的屏幕配置
     * 修复副主副屏切换时分辨率异常的问题
     */
    private void forceRefreshUIForScreenChange(Configuration newConfig) {
        try {
            Log.i(TAG, "forceRefreshUIForScreenChange: start, newConfig=" + newConfig);

            // 延迟执行，确保配置变化完全生效
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                try {
                    // 1. 强制重新布局ViewPager和导航栏
                    if (home_view_page != null) {
                        home_view_page.requestLayout();
                    }
                    if (mHnlHomeNavigation != null) {
                        // 强制重新布局导航栏，这会触发重新计算尺寸
                        mHnlHomeNavigation.requestLayout();
                        mHnlHomeNavigation.invalidate();
                    }

                    // 2. 强制刷新所有Fragment
                    if (homeDateFragments != null) {
                        for (ComperhensiveHomeDateFragment fragment : homeDateFragments) {
                            if (fragment != null && fragment.getView() != null) {
                                fragment.getView().requestLayout();
                            }
                        }
                    }

                    // 3. 强制重新执行showAccordingToScreen
                    showAccordingToScreen(ResUtil.getOrientation());

                    // 4. 刷新调试覆盖层
                    Activity activity = getActivity();
                    if (activity != null) {
                        TouchAreaDebugger.refreshDebugOverlay(activity);
                    }

                    Log.i(TAG, "forceRefreshUIForScreenChange: completed");
                } catch (Exception e) {
                    Log.e(TAG, "forceRefreshUIForScreenChange: error", e);
                }
            }, 150); // 延迟150ms确保配置变化完全生效

        } catch (Exception e) {
            Log.e(TAG, "forceRefreshUIForScreenChange: error", e);
        }
    }

    public boolean isReportFragment() {
        return true;
    }

    /**
     * 文字轮播组件是否已经开始了轮播，
     * 用于记录状态，等fragment切换回来后继续轮播
     */
    private boolean isTextBannerNeedContinuePlay = false;

    @Override
    public void onPause() {
        if (textBannerView != null && textBannerView.isStarted()) {
            isTextBannerNeedContinuePlay = true;
            textBannerView.stopViewAnimator();
        }
        super.onPause();
    }

    public static class NetWorkListener implements NetworkManager.INetworkReady {
        private WeakReference<HorizontalHomePlayerFragment> horizontalHomePlayerFragmentReference;

        public NetWorkListener(HorizontalHomePlayerFragment horizontalHomePlayerFragment) {
            horizontalHomePlayerFragmentReference = new WeakReference<>(horizontalHomePlayerFragment);
        }

        @Override
        public void networkChange(boolean hasNetwork) {
            try {
                HorizontalHomePlayerFragment horizontalHomePlayerFragment = horizontalHomePlayerFragmentReference.get();
                if (horizontalHomePlayerFragment == null) {
                    return;
                }
                if (!hasNetwork) {
                    return;
                }
                horizontalHomePlayerFragment.retryLoad();
                NetworkManager.getInstance().removeNetworkReadyListener(horizontalHomePlayerFragment.mNetWorkListener);
            } catch (Exception e) {
                Log.i(TAG, "error:" + e.getMessage());
            }
        }
    }

    /**
     * 监听主题变化事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onThemeChangeEvent(UserCenterInter.ThemeChangeEvent event) {
        Log.d(TAG, "收到主题变化事件: " + event.getTheme());

        String theme = (event == null) ? "" : event.getTheme();
        if (TextUtils.isEmpty(theme)) {
            return;
        }

        if ("isSameTheme".equals(theme)) {
            Log.d(TAG, "收到isSameTheme事件，延迟更新用户头像");
            updateUserLoginStateDelayed();
            return;
        }
        // 根据皮肤框架的主题事件更新UI
        if ("night.skin".equals(theme) || "day.skin".equals(theme)) {
            Log.d(TAG, "主题切换为: " + theme + "，延迟更新用户头像");
            updateUserLoginStateDelayed();
        }
    }
    //延迟用户头像的更新
    private void updateUserLoginStateDelayed() {
        new Handler(Looper.getMainLooper()).postDelayed(this::updateUserLoginState, 300);
    }
}